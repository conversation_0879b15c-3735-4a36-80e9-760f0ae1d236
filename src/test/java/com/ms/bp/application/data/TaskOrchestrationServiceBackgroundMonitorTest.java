package com.ms.bp.application.data;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.interfaces.dto.request.ExportRequest;
import com.ms.bp.interfaces.dto.request.ImportRequest;
import com.ms.bp.shared.common.constants.BusinessConstants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * TaskOrchestrationServiceの後台監視機能統合テスト
 * 実際の導入・導出処理での後台スレッド監視動作を検証する
 */
class TaskOrchestrationServiceBackgroundMonitorTest {

    @Mock
    private Context mockContext;

    private TaskOrchestrationService taskOrchestrationService;
    private UserInfo testUserInfo;
    private ExportRequest testExportRequest;
    private ImportRequest testImportRequest;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        taskOrchestrationService = new TaskOrchestrationService();

        // テスト用ユーザー情報を設定
        testUserInfo = UserInfo.builder()
                .shainCode("TEST001")
                .systemOperationCompanyCode(BusinessConstants.SYSTEM_OPERATION_COMPANY_CODE)
                .areaCode("0000")
                .build();

        // テスト用エクスポートリクエストを設定
        testExportRequest = ExportRequest.builder()
                .dataType(BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE)
                .areaString("0000")
                .hnshBashoKubun(BusinessConstants.AFFILIATION_HEAD_OFFICE)
                .dataKubunString(BusinessConstants.DATAKUBUN_IKO_BEFORE)
                .ctgryKubun("1")
                .build();

        // テスト用インポートリクエストを設定
        testImportRequest = ImportRequest.builder()
                .dataType(BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE)
                .s3Key("test/import/file.csv")
                .build();
    }

    @Test
    @DisplayName("正常ケース：十分な実行時間でのエクスポート処理（後台監視付き）")
    void testExportTaskWithBackgroundMonitoringSufficientTime() {
        // 準備：十分な残り時間を設定（10分）
        when(mockContext.getRemainingTimeInMillis()).thenReturn(600000L);

        // 実行：例外が投げられないことを確認
        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateExportTask(
                12345L, testExportRequest, testUserInfo, mockContext
            );
        });

        // 検証：Contextが適切に呼び出されたことを確認
        verify(mockContext, atLeastOnce()).getRemainingTimeInMillis();
    }

    @Test
    @DisplayName("正常ケース：十分な実行時間でのインポート処理（後台監視付き）")
    void testImportTaskWithBackgroundMonitoringSufficientTime() {
        // 準備：十分な残り時間を設定（10分）
        when(mockContext.getRemainingTimeInMillis()).thenReturn(600000L);

        // 実行：例外が投げられないことを確認
        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateImportTask(
                12345L, testImportRequest, testUserInfo, mockContext
            );
        });

        // 検証：Contextが適切に呼び出されたことを確認
        verify(mockContext, atLeastOnce()).getRemainingTimeInMillis();
    }

    @Test
    @DisplayName("正常ケース：後台監視でのタイムアウト接近検出（エクスポート）")
    void testExportTaskWithBackgroundTimeoutDetection() {
        // 準備：タイムアウト接近状態を設定
        // 最初は十分な時間、その後急激に減少してタイムアウト接近をシミュレート
        when(mockContext.getRemainingTimeInMillis())
                .thenReturn(600000L)  // 初期：10分
                .thenReturn(300000L)  // 1回目チェック：5分
                .thenReturn(120000L)  // 2回目チェック：2分
                .thenReturn(30000L);  // 3回目チェック：30秒（タイムアウト接近）

        // 実行：後台監視が適切に動作することを確認
        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateExportTask(
                12345L, testExportRequest, testUserInfo, mockContext
            );
        });

        // 検証：複数回のタイムアウトチェックが実行されたことを確認
        verify(mockContext, atLeast(2)).getRemainingTimeInMillis();
    }

    @Test
    @DisplayName("正常ケース：後台監視でのタイムアウト接近検出（インポート）")
    void testImportTaskWithBackgroundTimeoutDetection() {
        // 準備：タイムアウト接近状態を設定
        when(mockContext.getRemainingTimeInMillis())
                .thenReturn(600000L)  // 初期：10分
                .thenReturn(300000L)  // 1回目チェック：5分
                .thenReturn(120000L)  // 2回目チェック：2分
                .thenReturn(30000L);  // 3回目チェック：30秒（タイムアウト接近）

        // 実行：後台監視が適切に動作することを確認
        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateImportTask(
                12345L, testImportRequest, testUserInfo, mockContext
            );
        });

        // 検証：複数回のタイムアウトチェックが実行されたことを確認
        verify(mockContext, atLeast(2)).getRemainingTimeInMillis();
    }

    @Test
    @DisplayName("正常ケース：複数の並行エクスポートタスクでの独立監視")
    void testMultipleExportTasksWithIndependentBackgroundMonitoring() {
        // 準備：異なる残り時間を設定
        when(mockContext.getRemainingTimeInMillis())
                .thenReturn(600000L)  // 1つ目のタスク：10分
                .thenReturn(300000L); // 2つ目のタスク：5分

        // 実行：複数のタスクを実行
        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateExportTask(
                12345L, testExportRequest, testUserInfo, mockContext
            );
        });

        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateExportTask(
                12346L, testExportRequest, testUserInfo, mockContext
            );
        });

        // 検証：各タスクで後台監視が実行されたことを確認
        verify(mockContext, atLeast(2)).getRemainingTimeInMillis();
    }

    @Test
    @DisplayName("正常ケース：異なるデータタイプでの後台監視")
    void testDifferentDataTypesWithBackgroundMonitoring() {
        // 準備：十分な残り時間を設定
        when(mockContext.getRemainingTimeInMillis()).thenReturn(600000L);

        // 異なるデータタイプのリクエストを準備
        ExportRequest budgetRequest = ExportRequest.builder()
                .dataType(BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE)
                .areaString("0000")
                .hnshBashoKubun(BusinessConstants.AFFILIATION_HEAD_OFFICE)
                .dataKubunString(BusinessConstants.DATAKUBUN_IKO_BEFORE)
                .ctgryKubun("1")
                .build();

        // 実行：異なるデータタイプでの処理
        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateExportTask(
                12345L, testExportRequest, testUserInfo, mockContext
            );
        });

        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateExportTask(
                12346L, budgetRequest, testUserInfo, mockContext
            );
        });

        // 検証：各データタイプで後台監視が実行されたことを確認
        verify(mockContext, atLeast(2)).getRemainingTimeInMillis();
    }

    @Test
    @DisplayName("正常ケース：エラー発生時の後台監視クリーンアップ")
    void testBackgroundMonitoringCleanupOnError() {
        // 準備：十分な残り時間を設定
        when(mockContext.getRemainingTimeInMillis()).thenReturn(600000L);

        // 無効なリクエストでエラーを発生させる
        ExportRequest invalidRequest = ExportRequest.builder()
                .dataType(null) // 無効なデータタイプ
                .build();

        // 実行：エラーが発生しても適切に処理されることを確認
        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateExportTask(
                12345L, invalidRequest, testUserInfo, mockContext
            );
        });

        // 検証：エラー発生時でも後台監視が実行されたことを確認
        verify(mockContext, atLeastOnce()).getRemainingTimeInMillis();
    }

    @Test
    @DisplayName("正常ケース：長時間実行での定期的な後台監視チェック")
    void testPeriodicBackgroundMonitoringDuringLongExecution() {
        // 準備：長時間実行をシミュレートする残り時間設定
        when(mockContext.getRemainingTimeInMillis())
                .thenReturn(900000L)  // 開始時：15分
                .thenReturn(800000L)  // チェック1：13.3分
                .thenReturn(700000L)  // チェック2：11.7分
                .thenReturn(600000L)  // チェック3：10分
                .thenReturn(500000L)  // チェック4：8.3分
                .thenReturn(400000L)  // チェック5：6.7分
                .thenReturn(300000L); // チェック6：5分

        // 実行：長時間実行での処理
        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateExportTask(
                12345L, testExportRequest, testUserInfo, mockContext
            );
        });

        // 検証：複数回の後台監視チェックが実行されたことを確認
        verify(mockContext, atLeast(3)).getRemainingTimeInMillis();
    }

    @Test
    @DisplayName("正常ケース：インポートとエクスポートの混合処理での後台監視")
    void testMixedImportExportWithBackgroundMonitoring() {
        // 準備：十分な残り時間を設定
        when(mockContext.getRemainingTimeInMillis()).thenReturn(600000L);

        // 実行：インポートとエクスポートを交互に実行
        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateImportTask(
                12345L, testImportRequest, testUserInfo, mockContext
            );
        });

        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateExportTask(
                12346L, testExportRequest, testUserInfo, mockContext
            );
        });

        // 検証：両方の処理で後台監視が実行されたことを確認
        verify(mockContext, atLeast(2)).getRemainingTimeInMillis();
    }

    @Test
    @DisplayName("正常ケース：境界値でのタイムアウト監視（緩衝時間ちょうど）")
    void testBackgroundMonitoringAtBufferTimeBoundary() {
        // 準備：緩衝時間ちょうどの残り時間を設定（60秒）
        when(mockContext.getRemainingTimeInMillis()).thenReturn(60000L);

        // 実行：境界値での処理
        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateExportTask(
                12345L, testExportRequest, testUserInfo, mockContext
            );
        });

        // 検証：境界値でも後台監視が実行されたことを確認
        verify(mockContext, atLeastOnce()).getRemainingTimeInMillis();
    }

    @Test
    @DisplayName("正常ケース：非常に短い実行時間での即座のタイムアウト検出")
    void testImmediateTimeoutDetectionWithVeryShortTime() {
        // 準備：非常に短い残り時間を設定（10秒）
        when(mockContext.getRemainingTimeInMillis()).thenReturn(10000L);

        // 実行：即座にタイムアウト検出される処理
        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateExportTask(
                12345L, testExportRequest, testUserInfo, mockContext
            );
        });

        // 検証：短時間でも後台監視が実行されたことを確認
        verify(mockContext, atLeastOnce()).getRemainingTimeInMillis();
    }
}
