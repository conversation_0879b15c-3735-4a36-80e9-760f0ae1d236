package com.ms.bp.application.data;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.interfaces.dto.request.ExportRequest;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.exception.TimeoutApproachingException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * TaskOrchestrationServiceのタイムアウト監視機能統合テスト
 * 実際のエクスポート処理でのタイムアウト監視動作を検証する
 */
class TaskOrchestrationServiceTimeoutTest {

    @Mock
    private Context mockContext;

    private TaskOrchestrationService taskOrchestrationService;
    private UserInfo testUserInfo;
    private ExportRequest testExportRequest;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        taskOrchestrationService = new TaskOrchestrationService();

        // テスト用ユーザー情報を設定
        testUserInfo = UserInfo.builder()
                .shainCode("TEST001")
                .systemOperationCompanyCode(BusinessConstants.SYSTEM_OPERATION_COMPANY_CODE)
                .areaCode("0000")
                .build();

        // テスト用エクスポートリクエストを設定
        testExportRequest = ExportRequest.builder()
                .dataType(BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE)
                .areaString("0000")
                .hnshBashoKubun(BusinessConstants.AFFILIATION_HEAD_OFFICE)
                .dataKubunString(BusinessConstants.DATAKUBUN_IKO_BEFORE)
                .ctgryKubun("1")
                .build();
    }

    @Test
    @DisplayName("正常ケース：十分な実行時間でのエクスポート処理")
    void testExportTaskWithSufficientTime() {
        // 準備：十分な残り時間を設定（10分）
        when(mockContext.getRemainingTimeInMillis()).thenReturn(600000L);

        // 実行：例外が投げられないことを確認
        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateExportTask(
                12345L, testExportRequest, testUserInfo, mockContext
            );
        });

        // 検証：Contextが適切に呼び出されたことを確認
        verify(mockContext, atLeastOnce()).getRemainingTimeInMillis();
    }

    @Test
    @DisplayName("異常ケース：タイムアウト接近時のエクスポート処理中断")
    void testExportTaskWithTimeoutApproaching() {
        // 準備：タイムアウト接近状態を設定（30秒）
        when(mockContext.getRemainingTimeInMillis()).thenReturn(30000L);

        // 実行：TimeoutApproachingExceptionが適切に処理されることを確認
        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateExportTask(
                12345L, testExportRequest, testUserInfo, mockContext
            );
        });

        // 検証：タイムアウトチェックが実行されたことを確認
        verify(mockContext, atLeastOnce()).getRemainingTimeInMillis();
    }

    @Test
    @DisplayName("正常ケース：段階的な残り時間減少でのタイムアウト監視")
    void testExportTaskWithGradualTimeReduction() {
        // 準備：段階的に減少する残り時間を設定
        when(mockContext.getRemainingTimeInMillis())
                .thenReturn(300000L)  // 1回目：5分
                .thenReturn(240000L)  // 2回目：4分
                .thenReturn(180000L)  // 3回目：3分
                .thenReturn(120000L)  // 4回目：2分
                .thenReturn(90000L)   // 5回目：1.5分
                .thenReturn(30000L);  // 6回目：30秒（タイムアウト接近）

        // 実行：処理が適切に実行されることを確認
        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateExportTask(
                12345L, testExportRequest, testUserInfo, mockContext
            );
        });

        // 検証：複数回のタイムアウトチェックが実行されたことを確認
        verify(mockContext, atLeast(2)).getRemainingTimeInMillis();
    }

    @Test
    @DisplayName("正常ケース：カスタム緩衝時間での動作確認")
    void testExportTaskWithCustomBufferTime() {
        // 準備：カスタム緩衝時間のテスト（90秒の緩衝時間を想定）
        // 残り時間を80秒に設定（90秒緩衝時間より少ない）
        when(mockContext.getRemainingTimeInMillis()).thenReturn(80000L);

        // 実行：処理が適切に実行されることを確認
        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateExportTask(
                12345L, testExportRequest, testUserInfo, mockContext
            );
        });

        // 検証：タイムアウトチェックが実行されたことを確認
        verify(mockContext, atLeastOnce()).getRemainingTimeInMillis();
    }

    @Test
    @DisplayName("正常ケース：複数のエクスポートタスクでの独立したタイムアウト監視")
    void testMultipleExportTasksWithIndependentTimeoutMonitoring() {
        // 準備：異なる残り時間を設定
        when(mockContext.getRemainingTimeInMillis())
                .thenReturn(300000L)  // 1つ目のタスク：5分
                .thenReturn(180000L); // 2つ目のタスク：3分

        // 実行：複数のタスクを実行
        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateExportTask(
                12345L, testExportRequest, testUserInfo, mockContext
            );
        });

        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateExportTask(
                12346L, testExportRequest, testUserInfo, mockContext
            );
        });

        // 検証：各タスクでタイムアウトチェックが実行されたことを確認
        verify(mockContext, atLeast(2)).getRemainingTimeInMillis();
    }

    @Test
    @DisplayName("正常ケース：異なるデータタイプでのタイムアウト監視")
    void testDifferentDataTypesWithTimeoutMonitoring() {
        // 準備：十分な残り時間を設定
        when(mockContext.getRemainingTimeInMillis()).thenReturn(600000L);

        // 異なるデータタイプのリクエストを準備
        ExportRequest budgetRequest = ExportRequest.builder()
                .dataType(BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE)
                .areaString("0000")
                .hnshBashoKubun(BusinessConstants.AFFILIATION_HEAD_OFFICE)
                .dataKubunString(BusinessConstants.DATAKUBUN_IKO_BEFORE)
                .ctgryKubun("1")
                .build();

        // 実行：異なるデータタイプでの処理
        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateExportTask(
                12345L, testExportRequest, testUserInfo, mockContext
            );
        });

        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateExportTask(
                12346L, budgetRequest, testUserInfo, mockContext
            );
        });

        // 検証：各データタイプでタイムアウトチェックが実行されたことを確認
        verify(mockContext, atLeast(2)).getRemainingTimeInMillis();
    }

    @Test
    @DisplayName("正常ケース：エラー発生時のタイムアウト監視継続")
    void testTimeoutMonitoringContinuesOnError() {
        // 準備：十分な残り時間を設定
        when(mockContext.getRemainingTimeInMillis()).thenReturn(600000L);

        // 無効なリクエストでエラーを発生させる
        ExportRequest invalidRequest = ExportRequest.builder()
                .dataType(null) // 無効なデータタイプ
                .build();

        // 実行：エラーが発生しても適切に処理されることを確認
        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateExportTask(
                12345L, invalidRequest, testUserInfo, mockContext
            );
        });

        // 検証：エラー発生時でもタイムアウトチェックが実行されたことを確認
        verify(mockContext, atLeastOnce()).getRemainingTimeInMillis();
    }

    @Test
    @DisplayName("正常ケース：長時間実行での定期的なタイムアウトチェック")
    void testPeriodicTimeoutChecksDuringLongExecution() {
        // 準備：長時間実行をシミュレートする残り時間設定
        when(mockContext.getRemainingTimeInMillis())
                .thenReturn(900000L)  // 開始時：15分
                .thenReturn(800000L)  // チェック1：13.3分
                .thenReturn(700000L)  // チェック2：11.7分
                .thenReturn(600000L)  // チェック3：10分
                .thenReturn(500000L)  // チェック4：8.3分
                .thenReturn(400000L)  // チェック5：6.7分
                .thenReturn(300000L); // チェック6：5分

        // 実行：長時間実行での処理
        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateExportTask(
                12345L, testExportRequest, testUserInfo, mockContext
            );
        });

        // 検証：複数回のタイムアウトチェックが実行されたことを確認
        verify(mockContext, atLeast(3)).getRemainingTimeInMillis();
    }

    @Test
    @DisplayName("正常ケース：境界値でのタイムアウト監視（緩衝時間ちょうど）")
    void testTimeoutMonitoringAtBufferTimeBoundary() {
        // 準備：緩衝時間ちょうどの残り時間を設定（60秒）
        when(mockContext.getRemainingTimeInMillis()).thenReturn(60000L);

        // 実行：境界値での処理
        assertDoesNotThrow(() -> {
            taskOrchestrationService.orchestrateExportTask(
                12345L, testExportRequest, testUserInfo, mockContext
            );
        });

        // 検証：境界値でもタイムアウトチェックが実行されたことを確認
        verify(mockContext, atLeastOnce()).getRemainingTimeInMillis();
    }
}
