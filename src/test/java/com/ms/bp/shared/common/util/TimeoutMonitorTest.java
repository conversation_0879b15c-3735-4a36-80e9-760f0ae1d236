package com.ms.bp.shared.common.util;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.shared.common.exception.TimeoutApproachingException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * TimeoutMonitorクラスの単体テスト
 * Lambda実行時間監視機能の各種シナリオをテストする
 */
class TimeoutMonitorTest {

    @Mock
    private Context mockContext;

    private static final String TEST_JOB_ID = "12345";
    private static final long DEFAULT_BUFFER_TIME_MS = 60000; // 60秒

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("正常ケース：デフォルト緩衝時間でのTimeoutMonitor初期化")
    void testTimeoutMonitorInitializationWithDefaultBuffer() {
        // 準備：十分な残り時間を設定
        when(mockContext.getRemainingTimeInMillis()).thenReturn(300000L); // 5分

        // 実行
        TimeoutMonitor timeoutMonitor = new TimeoutMonitor(mockContext, TEST_JOB_ID);

        // 検証
        assertEquals(TEST_JOB_ID, timeoutMonitor.getJobId());
        assertEquals(DEFAULT_BUFFER_TIME_MS, timeoutMonitor.getBufferTimeMs());
        assertFalse(timeoutMonitor.isTimeoutApproaching());
    }

    @Test
    @DisplayName("正常ケース：カスタム緩衝時間でのTimeoutMonitor初期化")
    void testTimeoutMonitorInitializationWithCustomBuffer() {
        // 準備
        long customBufferTime = 90000L; // 90秒
        when(mockContext.getRemainingTimeInMillis()).thenReturn(300000L);

        // 実行
        TimeoutMonitor timeoutMonitor = new TimeoutMonitor(mockContext, TEST_JOB_ID, customBufferTime);

        // 検証
        assertEquals(customBufferTime, timeoutMonitor.getBufferTimeMs());
        assertFalse(timeoutMonitor.isTimeoutApproaching());
    }

    @Test
    @DisplayName("異常ケース：null Contextでの初期化")
    void testTimeoutMonitorInitializationWithNullContext() {
        // 実行・検証
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> new TimeoutMonitor(null, TEST_JOB_ID)
        );
        assertEquals("Lambda contextは必須です", exception.getMessage());
    }

    @Test
    @DisplayName("異常ケース：null JobIDでの初期化")
    void testTimeoutMonitorInitializationWithNullJobId() {
        // 実行・検証
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> new TimeoutMonitor(mockContext, null)
        );
        assertEquals("ジョブIDは必須です", exception.getMessage());
    }

    @Test
    @DisplayName("異常ケース：空文字JobIDでの初期化")
    void testTimeoutMonitorInitializationWithEmptyJobId() {
        // 実行・検証
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> new TimeoutMonitor(mockContext, "   ")
        );
        assertEquals("ジョブIDは必須です", exception.getMessage());
    }

    @Test
    @DisplayName("異常ケース：負の緩衝時間での初期化")
    void testTimeoutMonitorInitializationWithNegativeBuffer() {
        // 実行・検証
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> new TimeoutMonitor(mockContext, TEST_JOB_ID, -1000L)
        );
        assertEquals("緩衝時間は0以上である必要があります", exception.getMessage());
    }

    @Test
    @DisplayName("正常ケース：タイムアウト接近していない場合のチェック")
    void testCheckTimeoutWhenNotApproaching() {
        // 準備：十分な残り時間を設定
        when(mockContext.getRemainingTimeInMillis()).thenReturn(300000L); // 5分
        TimeoutMonitor timeoutMonitor = new TimeoutMonitor(mockContext, TEST_JOB_ID);

        // 実行・検証：例外が投げられないことを確認
        assertDoesNotThrow(() -> timeoutMonitor.checkTimeout("テストチェックポイント"));
        assertFalse(timeoutMonitor.isTimeoutApproaching());
    }

    @Test
    @DisplayName("異常ケース：タイムアウト接近時のチェック")
    void testCheckTimeoutWhenApproaching() {
        // 準備：緩衝時間以下の残り時間を設定
        when(mockContext.getRemainingTimeInMillis()).thenReturn(30000L); // 30秒（緩衝時間60秒より少ない）
        TimeoutMonitor timeoutMonitor = new TimeoutMonitor(mockContext, TEST_JOB_ID);

        // 実行・検証
        TimeoutApproachingException exception = assertThrows(
            TimeoutApproachingException.class,
            () -> timeoutMonitor.checkTimeout("テストチェックポイント")
        );

        assertEquals(TEST_JOB_ID, exception.getJobId());
        assertEquals("テストチェックポイント", exception.getCheckPointName());
        assertEquals(30000L, exception.getRemainingTimeMs());
        assertTrue(timeoutMonitor.isTimeoutApproaching());
    }

    @Test
    @DisplayName("正常ケース：残り時間取得")
    void testGetRemainingTimeMs() {
        // 準備
        long expectedRemainingTime = 180000L; // 3分
        when(mockContext.getRemainingTimeInMillis()).thenReturn(expectedRemainingTime);
        TimeoutMonitor timeoutMonitor = new TimeoutMonitor(mockContext, TEST_JOB_ID);

        // 実行・検証
        assertEquals(expectedRemainingTime, timeoutMonitor.getRemainingTimeMs());
    }

    @Test
    @DisplayName("正常ケース：経過時間取得")
    void testGetElapsedTimeMs() throws InterruptedException {
        // 準備
        when(mockContext.getRemainingTimeInMillis()).thenReturn(300000L);
        TimeoutMonitor timeoutMonitor = new TimeoutMonitor(mockContext, TEST_JOB_ID);

        // 実行：少し待機
        Thread.sleep(100);
        long elapsedTime = timeoutMonitor.getElapsedTimeMs();

        // 検証：経過時間が100ms以上であることを確認
        assertTrue(elapsedTime >= 100);
    }

    @Test
    @DisplayName("正常ケース：安全実行可能性チェック（実行可能）")
    void testCanSafelyExecuteWhenSafe() {
        // 準備：十分な残り時間を設定
        when(mockContext.getRemainingTimeInMillis()).thenReturn(300000L); // 5分
        TimeoutMonitor timeoutMonitor = new TimeoutMonitor(mockContext, TEST_JOB_ID);

        // 実行・検証：2分の処理は安全に実行可能
        long estimatedProcessingTime = 120000L; // 2分
        assertTrue(timeoutMonitor.canSafelyExecute(estimatedProcessingTime));
    }

    @Test
    @DisplayName("正常ケース：安全実行可能性チェック（実行不可能）")
    void testCanSafelyExecuteWhenUnsafe() {
        // 準備：少ない残り時間を設定
        when(mockContext.getRemainingTimeInMillis()).thenReturn(90000L); // 1.5分
        TimeoutMonitor timeoutMonitor = new TimeoutMonitor(mockContext, TEST_JOB_ID);

        // 実行・検証：5分の処理は安全に実行不可能
        long estimatedProcessingTime = 300000L; // 5分
        assertFalse(timeoutMonitor.canSafelyExecute(estimatedProcessingTime));
    }

    @Test
    @DisplayName("正常ケース：警告レベル取得（INFO）")
    void testGetWarningLevelInfo() {
        // 準備：十分な残り時間を設定
        when(mockContext.getRemainingTimeInMillis()).thenReturn(300000L); // 5分
        TimeoutMonitor timeoutMonitor = new TimeoutMonitor(mockContext, TEST_JOB_ID);

        // 実行・検証
        assertEquals("INFO", timeoutMonitor.getWarningLevel());
    }

    @Test
    @DisplayName("正常ケース：警告レベル取得（WARN）")
    void testGetWarningLevelWarn() {
        // 準備：警告レベルの残り時間を設定（緩衝時間の2倍以下）
        when(mockContext.getRemainingTimeInMillis()).thenReturn(100000L); // 100秒（緩衝時間60秒の2倍以下）
        TimeoutMonitor timeoutMonitor = new TimeoutMonitor(mockContext, TEST_JOB_ID);

        // 実行・検証
        assertEquals("WARN", timeoutMonitor.getWarningLevel());
    }

    @Test
    @DisplayName("正常ケース：警告レベル取得（ERROR）")
    void testGetWarningLevelError() {
        // 準備：エラーレベルの残り時間を設定（緩衝時間以下）
        when(mockContext.getRemainingTimeInMillis()).thenReturn(30000L); // 30秒（緩衝時間60秒以下）
        TimeoutMonitor timeoutMonitor = new TimeoutMonitor(mockContext, TEST_JOB_ID);

        // 実行・検証
        assertEquals("ERROR", timeoutMonitor.getWarningLevel());
    }

    @Test
    @DisplayName("正常ケース：実行時間統計ログ出力")
    void testLogExecutionStats() {
        // 準備
        when(mockContext.getRemainingTimeInMillis()).thenReturn(240000L); // 4分
        TimeoutMonitor timeoutMonitor = new TimeoutMonitor(mockContext, TEST_JOB_ID);

        // 実行：例外が投げられないことを確認
        assertDoesNotThrow(() -> timeoutMonitor.logExecutionStats());
    }

    @Test
    @DisplayName("正常ケース：複数回のタイムアウトチェック")
    void testMultipleTimeoutChecks() {
        // 準備：段階的に減少する残り時間を設定
        when(mockContext.getRemainingTimeInMillis())
            .thenReturn(300000L) // 1回目：5分
            .thenReturn(180000L) // 2回目：3分
            .thenReturn(120000L) // 3回目：2分
            .thenReturn(30000L); // 4回目：30秒（タイムアウト接近）

        TimeoutMonitor timeoutMonitor = new TimeoutMonitor(mockContext, TEST_JOB_ID);

        // 実行・検証
        assertDoesNotThrow(() -> timeoutMonitor.checkTimeout("チェック1"));
        assertDoesNotThrow(() -> timeoutMonitor.checkTimeout("チェック2"));
        assertDoesNotThrow(() -> timeoutMonitor.checkTimeout("チェック3"));

        // 4回目でタイムアウト接近例外が投げられることを確認
        TimeoutApproachingException exception = assertThrows(
            TimeoutApproachingException.class,
            () -> timeoutMonitor.checkTimeout("チェック4")
        );
        assertEquals("チェック4", exception.getCheckPointName());
    }
}
