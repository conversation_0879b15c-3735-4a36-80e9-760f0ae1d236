package com.ms.bp.shared.common.exception;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TimeoutApproachingExceptionクラスの単体テスト
 * Lambda実行時間タイムアウト接近例外の各種機能をテストする
 */
class TimeoutApproachingExceptionTest {

    private static final String TEST_JOB_ID = "12345";
    private static final String TEST_CHECKPOINT = "テストチェックポイント";
    private static final long TEST_REMAINING_TIME_MS = 30000L; // 30秒
    private static final long TEST_ELAPSED_TIME_MS = 270000L; // 4.5分

    @Test
    @DisplayName("正常ケース：基本コンストラクタでの例外作成")
    void testBasicConstructor() {
        // 実行
        TimeoutApproachingException exception = new TimeoutApproachingException(
            TEST_JOB_ID, TEST_CHECKPOINT, TEST_REMAINING_TIME_MS, TEST_ELAPSED_TIME_MS
        );

        // 検証
        assertEquals(TEST_JOB_ID, exception.getJobId());
        assertEquals(TEST_CHECKPOINT, exception.getCheckPointName());
        assertEquals(TEST_REMAINING_TIME_MS, exception.getRemainingTimeMs());
        assertEquals(TEST_ELAPSED_TIME_MS, exception.getElapsedTimeMs());
        assertNotNull(exception.getMessage());
        assertTrue(exception.getMessage().contains(TEST_JOB_ID));
        assertTrue(exception.getMessage().contains(TEST_CHECKPOINT));
    }

    @Test
    @DisplayName("正常ケース：原因例外付きコンストラクタでの例外作成")
    void testConstructorWithCause() {
        // 準備
        RuntimeException cause = new RuntimeException("原因例外");

        // 実行
        TimeoutApproachingException exception = new TimeoutApproachingException(
            TEST_JOB_ID, TEST_CHECKPOINT, TEST_REMAINING_TIME_MS, TEST_ELAPSED_TIME_MS, cause
        );

        // 検証
        assertEquals(TEST_JOB_ID, exception.getJobId());
        assertEquals(TEST_CHECKPOINT, exception.getCheckPointName());
        assertEquals(cause, exception.getCause());
        assertNotNull(exception.getMessage());
    }

    @Test
    @DisplayName("正常ケース：残り時間の秒単位取得")
    void testGetRemainingTimeSeconds() {
        // 実行
        TimeoutApproachingException exception = new TimeoutApproachingException(
            TEST_JOB_ID, TEST_CHECKPOINT, TEST_REMAINING_TIME_MS, TEST_ELAPSED_TIME_MS
        );

        // 検証：30000ms = 30秒
        assertEquals(30L, exception.getRemainingTimeSeconds());
    }

    @Test
    @DisplayName("正常ケース：経過時間の秒単位取得")
    void testGetElapsedTimeSeconds() {
        // 実行
        TimeoutApproachingException exception = new TimeoutApproachingException(
            TEST_JOB_ID, TEST_CHECKPOINT, TEST_REMAINING_TIME_MS, TEST_ELAPSED_TIME_MS
        );

        // 検証：270000ms = 270秒
        assertEquals(270L, exception.getElapsedTimeSeconds());
    }

    @Test
    @DisplayName("正常ケース：実行時間使用率の計算")
    void testGetTimeUsagePercentage() {
        // 実行
        TimeoutApproachingException exception = new TimeoutApproachingException(
            TEST_JOB_ID, TEST_CHECKPOINT, TEST_REMAINING_TIME_MS, TEST_ELAPSED_TIME_MS
        );

        // 検証：270000 / (30000 + 270000) * 100 = 90.0%
        assertEquals(90.0, exception.getTimeUsagePercentage(), 0.1);
    }

    @Test
    @DisplayName("正常ケース：総時間が0の場合の使用率計算")
    void testGetTimeUsagePercentageWithZeroTotal() {
        // 実行
        TimeoutApproachingException exception = new TimeoutApproachingException(
            TEST_JOB_ID, TEST_CHECKPOINT, 0L, 0L
        );

        // 検証：総時間が0の場合は0.0%
        assertEquals(0.0, exception.getTimeUsagePercentage(), 0.1);
    }

    @Test
    @DisplayName("正常ケース：タイムアウトまでの推定時間取得")
    void testGetEstimatedTimeToTimeout() {
        // 実行
        TimeoutApproachingException exception = new TimeoutApproachingException(
            TEST_JOB_ID, TEST_CHECKPOINT, TEST_REMAINING_TIME_MS, TEST_ELAPSED_TIME_MS
        );

        // 検証
        assertEquals(TEST_REMAINING_TIME_MS, exception.getEstimatedTimeToTimeout());
    }

    @Test
    @DisplayName("正常ケース：詳細情報文字列の取得")
    void testGetDetailedInfo() {
        // 実行
        TimeoutApproachingException exception = new TimeoutApproachingException(
            TEST_JOB_ID, TEST_CHECKPOINT, TEST_REMAINING_TIME_MS, TEST_ELAPSED_TIME_MS
        );

        // 検証
        String detailedInfo = exception.getDetailedInfo();
        assertNotNull(detailedInfo);
        assertTrue(detailedInfo.contains(TEST_JOB_ID));
        assertTrue(detailedInfo.contains(TEST_CHECKPOINT));
        assertTrue(detailedInfo.contains("30000")); // 残り時間（ms）
        assertTrue(detailedInfo.contains("270000")); // 経過時間（ms）
        assertTrue(detailedInfo.contains("30")); // 残り時間（秒）
        assertTrue(detailedInfo.contains("270")); // 経過時間（秒）
        assertTrue(detailedInfo.contains("90.0")); // 使用率
    }

    @Test
    @DisplayName("正常ケース：ログ用情報文字列の取得")
    void testGetLogInfo() {
        // 実行
        TimeoutApproachingException exception = new TimeoutApproachingException(
            TEST_JOB_ID, TEST_CHECKPOINT, TEST_REMAINING_TIME_MS, TEST_ELAPSED_TIME_MS
        );

        // 検証
        String logInfo = exception.getLogInfo();
        assertNotNull(logInfo);
        assertTrue(logInfo.contains(TEST_JOB_ID));
        assertTrue(logInfo.contains(TEST_CHECKPOINT));
        assertTrue(logInfo.contains("30000")); // 残り時間
        assertTrue(logInfo.contains("270000")); // 経過時間
        assertTrue(logInfo.contains("90.0")); // 使用率
    }

    @Test
    @DisplayName("正常ケース：緊急レベル判定（緊急レベル）")
    void testIsCriticalLevelTrue() {
        // 準備：残り時間30秒以下
        TimeoutApproachingException exception = new TimeoutApproachingException(
            TEST_JOB_ID, TEST_CHECKPOINT, 25000L, TEST_ELAPSED_TIME_MS
        );

        // 検証
        assertTrue(exception.isCriticalLevel());
    }

    @Test
    @DisplayName("正常ケース：緊急レベル判定（非緊急レベル）")
    void testIsCriticalLevelFalse() {
        // 準備：残り時間30秒超
        TimeoutApproachingException exception = new TimeoutApproachingException(
            TEST_JOB_ID, TEST_CHECKPOINT, 45000L, TEST_ELAPSED_TIME_MS
        );

        // 検証
        assertFalse(exception.isCriticalLevel());
    }

    @Test
    @DisplayName("正常ケース：緊急レベル判定（境界値：30秒ちょうど）")
    void testIsCriticalLevelBoundary() {
        // 準備：残り時間30秒ちょうど
        TimeoutApproachingException exception = new TimeoutApproachingException(
            TEST_JOB_ID, TEST_CHECKPOINT, 30000L, TEST_ELAPSED_TIME_MS
        );

        // 検証：30秒ちょうどは緊急レベルではない
        assertFalse(exception.isCriticalLevel());
    }

    @Test
    @DisplayName("正常ケース：ServiceExceptionへの変換")
    void testToServiceException() {
        // 実行
        TimeoutApproachingException exception = new TimeoutApproachingException(
            TEST_JOB_ID, TEST_CHECKPOINT, TEST_REMAINING_TIME_MS, TEST_ELAPSED_TIME_MS
        );

        ServiceException serviceException = exception.toServiceException();

        // 検証
        assertNotNull(serviceException);
        assertEquals(50002, serviceException.getCode().intValue());
        assertEquals(exception.getMessage(), serviceException.getMessage());
    }

    @Test
    @DisplayName("正常ケース：例外メッセージの内容確認")
    void testExceptionMessage() {
        // 実行
        TimeoutApproachingException exception = new TimeoutApproachingException(
            TEST_JOB_ID, TEST_CHECKPOINT, TEST_REMAINING_TIME_MS, TEST_ELAPSED_TIME_MS
        );

        // 検証
        String message = exception.getMessage();
        assertNotNull(message);
        assertTrue(message.contains("Lambda実行時間がタイムアウトに接近しました"));
        assertTrue(message.contains("jobId=" + TEST_JOB_ID));
        assertTrue(message.contains("checkPoint=" + TEST_CHECKPOINT));
        assertTrue(message.contains("remainingTime=" + TEST_REMAINING_TIME_MS + "ms"));
        assertTrue(message.contains("elapsedTime=" + TEST_ELAPSED_TIME_MS + "ms"));
    }

    @Test
    @DisplayName("正常ケース：例外時刻の設定確認")
    void testExceptionTime() {
        // 準備
        long beforeTime = System.currentTimeMillis();

        // 実行
        TimeoutApproachingException exception = new TimeoutApproachingException(
            TEST_JOB_ID, TEST_CHECKPOINT, TEST_REMAINING_TIME_MS, TEST_ELAPSED_TIME_MS
        );

        // 検証
        long afterTime = System.currentTimeMillis();
        assertTrue(exception.getExceptionTimeMs() >= beforeTime);
        assertTrue(exception.getExceptionTimeMs() <= afterTime);
    }

    @Test
    @DisplayName("正常ケース：複数の例外インスタンスの独立性確認")
    void testMultipleInstancesIndependence() {
        // 実行
        TimeoutApproachingException exception1 = new TimeoutApproachingException(
            "job1", "checkpoint1", 10000L, 100000L
        );
        TimeoutApproachingException exception2 = new TimeoutApproachingException(
            "job2", "checkpoint2", 20000L, 200000L
        );

        // 検証：各インスタンスが独立していることを確認
        assertNotEquals(exception1.getJobId(), exception2.getJobId());
        assertNotEquals(exception1.getCheckPointName(), exception2.getCheckPointName());
        assertNotEquals(exception1.getRemainingTimeMs(), exception2.getRemainingTimeMs());
        assertNotEquals(exception1.getElapsedTimeMs(), exception2.getElapsedTimeMs());
        assertNotEquals(exception1.getTimeUsagePercentage(), exception2.getTimeUsagePercentage());
    }
}
