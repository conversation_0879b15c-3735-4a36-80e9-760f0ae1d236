package com.ms.bp.shared.common.util;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.shared.common.constants.BusinessConstants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * BackgroundTimeoutMonitorクラスの単体テスト
 * 後台スレッド監視機能の各種シナリオをテストする
 */
class BackgroundTimeoutMonitorTest {

    @Mock
    private Context mockContext;

    private static final String TEST_JOB_ID = "12345";
    private static final String EXPORT_OPERATION = BusinessConstants.OPERATION_DOWNLOAD_CODE;
    private static final String IMPORT_OPERATION = BusinessConstants.OPERATION_UPLOAD_CODE;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("正常ケース：エクスポート操作での後台監視初期化")
    void testBackgroundMonitorInitializationForExport() {
        // 準備：十分な残り時間を設定
        when(mockContext.getRemainingTimeInMillis()).thenReturn(600000L); // 10分

        // 実行
        BackgroundTimeoutMonitor monitor = new BackgroundTimeoutMonitor(
            mockContext, TEST_JOB_ID, EXPORT_OPERATION);

        // 検証
        assertFalse(monitor.isTimeoutDetected());
        assertNotNull(monitor);
    }

    @Test
    @DisplayName("正常ケース：インポート操作での後台監視初期化")
    void testBackgroundMonitorInitializationForImport() {
        // 準備：十分な残り時間を設定
        when(mockContext.getRemainingTimeInMillis()).thenReturn(600000L);

        // 実行
        BackgroundTimeoutMonitor monitor = new BackgroundTimeoutMonitor(
            mockContext, TEST_JOB_ID, IMPORT_OPERATION);

        // 検証
        assertFalse(monitor.isTimeoutDetected());
        assertNotNull(monitor);
    }

    @Test
    @DisplayName("異常ケース：null Contextでの初期化")
    void testBackgroundMonitorInitializationWithNullContext() {
        // 実行・検証
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> new BackgroundTimeoutMonitor(null, TEST_JOB_ID, EXPORT_OPERATION)
        );
        assertEquals("Lambda contextは必須です", exception.getMessage());
    }

    @Test
    @DisplayName("異常ケース：null JobIDでの初期化")
    void testBackgroundMonitorInitializationWithNullJobId() {
        // 実行・検証
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> new BackgroundTimeoutMonitor(mockContext, null, EXPORT_OPERATION)
        );
        assertEquals("ジョブIDは必須です", exception.getMessage());
    }

    @Test
    @DisplayName("異常ケース：null操作タイプでの初期化")
    void testBackgroundMonitorInitializationWithNullOperationType() {
        // 実行・検証
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> new BackgroundTimeoutMonitor(mockContext, TEST_JOB_ID, null)
        );
        assertEquals("操作タイプは必須です", exception.getMessage());
    }

    @Test
    @DisplayName("正常ケース：後台監視開始と停止")
    void testStartAndStopMonitoring() throws InterruptedException {
        // 準備：十分な残り時間を設定
        when(mockContext.getRemainingTimeInMillis()).thenReturn(600000L);
        BackgroundTimeoutMonitor monitor = new BackgroundTimeoutMonitor(
            mockContext, TEST_JOB_ID, EXPORT_OPERATION);

        // 実行：監視開始
        monitor.startMonitoring();
        
        // 少し待機してスレッドが開始されることを確認
        Thread.sleep(100);
        
        // 実行：監視停止
        monitor.stopMonitoring();
        
        // 検証：例外が投げられないことを確認
        assertDoesNotThrow(() -> monitor.cleanup());
    }

    @Test
    @DisplayName("正常ケース：処理フェーズの更新")
    void testUpdateCurrentPhase() {
        // 準備
        when(mockContext.getRemainingTimeInMillis()).thenReturn(600000L);
        BackgroundTimeoutMonitor monitor = new BackgroundTimeoutMonitor(
            mockContext, TEST_JOB_ID, EXPORT_OPERATION);

        // 実行・検証：例外が投げられないことを確認
        assertDoesNotThrow(() -> monitor.updateCurrentPhase("テストフェーズ"));
        assertDoesNotThrow(() -> monitor.updateCurrentPhase("データ処理"));
        assertDoesNotThrow(() -> monitor.updateCurrentPhase("ファイル生成"));
    }

    @Test
    @DisplayName("正常ケース：監視統計ログ出力")
    void testLogMonitoringStats() {
        // 準備
        when(mockContext.getRemainingTimeInMillis()).thenReturn(300000L); // 5分
        BackgroundTimeoutMonitor monitor = new BackgroundTimeoutMonitor(
            mockContext, TEST_JOB_ID, EXPORT_OPERATION);

        // 実行・検証：例外が投げられないことを確認
        assertDoesNotThrow(() -> monitor.logMonitoringStats());
    }

    @Test
    @DisplayName("正常ケース：重複監視開始の防止")
    void testPreventDuplicateMonitoringStart() throws InterruptedException {
        // 準備
        when(mockContext.getRemainingTimeInMillis()).thenReturn(600000L);
        BackgroundTimeoutMonitor monitor = new BackgroundTimeoutMonitor(
            mockContext, TEST_JOB_ID, EXPORT_OPERATION);

        // 実行：最初の監視開始
        monitor.startMonitoring();
        Thread.sleep(100);
        
        // 実行：重複監視開始（警告ログが出力されるが例外は投げられない）
        assertDoesNotThrow(() -> monitor.startMonitoring());
        
        // クリーンアップ
        monitor.cleanup();
    }

    @Test
    @DisplayName("正常ケース：クリーンアップの冪等性")
    void testCleanupIdempotency() {
        // 準備
        when(mockContext.getRemainingTimeInMillis()).thenReturn(600000L);
        BackgroundTimeoutMonitor monitor = new BackgroundTimeoutMonitor(
            mockContext, TEST_JOB_ID, EXPORT_OPERATION);

        // 実行：複数回のクリーンアップ
        assertDoesNotThrow(() -> monitor.cleanup());
        assertDoesNotThrow(() -> monitor.cleanup());
        assertDoesNotThrow(() -> monitor.cleanup());
    }

    @Test
    @DisplayName("正常ケース：カスタム緩衝時間での初期化")
    void testCustomBufferTimeInitialization() {
        // 準備
        long customBufferTime = 90000L; // 90秒
        when(mockContext.getRemainingTimeInMillis()).thenReturn(600000L);

        // 実行
        BackgroundTimeoutMonitor monitor = new BackgroundTimeoutMonitor(
            mockContext, TEST_JOB_ID, EXPORT_OPERATION, customBufferTime);

        // 検証
        assertNotNull(monitor);
        assertFalse(monitor.isTimeoutDetected());
    }

    @Test
    @DisplayName("異常ケース：負の緩衝時間での初期化")
    void testNegativeBufferTimeInitialization() {
        // 実行・検証
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> new BackgroundTimeoutMonitor(mockContext, TEST_JOB_ID, EXPORT_OPERATION, -1000L)
        );
        assertEquals("緩衝時間は0以上である必要があります", exception.getMessage());
    }

    @Test
    @DisplayName("正常ケース：タイムアウト検出フラグの初期状態")
    void testInitialTimeoutDetectionState() {
        // 準備
        when(mockContext.getRemainingTimeInMillis()).thenReturn(600000L);
        BackgroundTimeoutMonitor monitor = new BackgroundTimeoutMonitor(
            mockContext, TEST_JOB_ID, EXPORT_OPERATION);

        // 検証：初期状態ではタイムアウト未検出
        assertFalse(monitor.isTimeoutDetected());
    }

    @Test
    @DisplayName("正常ケース：監視なしでのクリーンアップ")
    void testCleanupWithoutMonitoring() {
        // 準備
        when(mockContext.getRemainingTimeInMillis()).thenReturn(600000L);
        BackgroundTimeoutMonitor monitor = new BackgroundTimeoutMonitor(
            mockContext, TEST_JOB_ID, EXPORT_OPERATION);

        // 実行：監視を開始せずにクリーンアップ
        assertDoesNotThrow(() -> monitor.cleanup());
    }

    @Test
    @DisplayName("正常ケース：異なる操作タイプでの監視")
    void testMonitoringWithDifferentOperationTypes() {
        // 準備
        when(mockContext.getRemainingTimeInMillis()).thenReturn(600000L);

        // エクスポート監視
        BackgroundTimeoutMonitor exportMonitor = new BackgroundTimeoutMonitor(
            mockContext, TEST_JOB_ID + "_export", EXPORT_OPERATION);
        
        // インポート監視
        BackgroundTimeoutMonitor importMonitor = new BackgroundTimeoutMonitor(
            mockContext, TEST_JOB_ID + "_import", IMPORT_OPERATION);

        // 検証：両方とも正常に初期化される
        assertNotNull(exportMonitor);
        assertNotNull(importMonitor);
        assertFalse(exportMonitor.isTimeoutDetected());
        assertFalse(importMonitor.isTimeoutDetected());
        
        // クリーンアップ
        exportMonitor.cleanup();
        importMonitor.cleanup();
    }

    @Test
    @DisplayName("正常ケース：長いジョブIDでの初期化")
    void testLongJobIdInitialization() {
        // 準備
        String longJobId = "1234567890".repeat(10); // 100文字のジョブID
        when(mockContext.getRemainingTimeInMillis()).thenReturn(600000L);

        // 実行
        BackgroundTimeoutMonitor monitor = new BackgroundTimeoutMonitor(
            mockContext, longJobId, EXPORT_OPERATION);

        // 検証
        assertNotNull(monitor);
        assertFalse(monitor.isTimeoutDetected());
        
        // クリーンアップ
        monitor.cleanup();
    }

    @Test
    @DisplayName("正常ケース：ゼロ緩衝時間での初期化")
    void testZeroBufferTimeInitialization() {
        // 準備
        when(mockContext.getRemainingTimeInMillis()).thenReturn(600000L);

        // 実行
        BackgroundTimeoutMonitor monitor = new BackgroundTimeoutMonitor(
            mockContext, TEST_JOB_ID, EXPORT_OPERATION, 0L);

        // 検証
        assertNotNull(monitor);
        assertFalse(monitor.isTimeoutDetected());
        
        // クリーンアップ
        monitor.cleanup();
    }
}
