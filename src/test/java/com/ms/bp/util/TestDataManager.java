package com.ms.bp.util;

import com.ms.bp.application.factory.DomainServiceFactory;
import com.ms.bp.shared.common.db.JdbcTemplate;
import com.ms.bp.shared.util.LambdaResourceManager;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;

/**
 * 次年度計画マスタエクスポート用Excel テストデータ管理クラス
 * executeExportTask メソッドの集成テスト用データ管理を担当
 */
public class TestDataManager {
    private static final Logger logger = LoggerFactory.getLogger(TestDataManager.class);
    
    private final String excelFileName;
    // 各テーブルの主キー情報
    private static final Map<String, String> TABLE_PRIMARY_KEYS;

    static {
        Map<String, String> primaryKeys = new LinkedHashMap<>();
        primaryKeys.put("T_DWNLD_RRK", "RRK_BANGO");
        primaryKeys.put("M_SOSHIKIAREAMST", "AREA_CODE,SUB_AREA_CODE,KSHB");
        primaryKeys.put("M_SK_KKK_AREA", "NENDO,SYSTM_UNYO_KIGYO_CODE,AREA_CODE");
        primaryKeys.put("T_JINENDO_KKK", "NENDO,SSNKN_TNCD,GROUP_CODE");
        primaryKeys.put("M_SAISANKANRITANIMST", "SSNKN_TNCD,KSHB");
        primaryKeys.put("M_CATEGORYMST", "CTGRY_CODE,SUB_CTGRY_CODE");
        primaryKeys.put("M_GROUPMST", "GROUP_CODE,KSHB");
        primaryKeys.put("M_UNITMST", "UNIT_CODE,KSHB");
        primaryKeys.put("M_KIGYOMST", "KIGYO_CODE");
        primaryKeys.put("M_TANTOSHAMST", "UNIT_CODE,TNTSH_CODE");
        primaryKeys.put("M_GYT_KANRI", "GYT_CODE,SMK_CODE,KSHB");
        primaryKeys.put("M_GYT_MEI", "GYT_SHK_NO");
        primaryKeys.put("M_SAISANKANRITANIFZKKMKMST", "SSNKN_TNCD,KSHB");

        primaryKeys.put("M_KEN_PERSON", "KEN_CODE,SYSTM_UNYO_KIGYO_CODE,SHAIN_CODE,KSHB");
        primaryKeys.put("M_KEN_RULE", "SYSTM_UNYO_KIGYO_CODE,HANT_CODE,SHR_CODE,KSHB");
        primaryKeys.put("M_KENGEN", "KEN_CODE,SYSTM_UNYO_KIGYO_CODE, hant_pttrn, HANT_CODE,KSHB");
        primaryKeys.put("M_MAKERMST", "MAKER_CODE");
        primaryKeys.put("M_SHAINMST", "SYSTM_UNYO_KIGYO_CODE,SHAIN_CODE");
        primaryKeys.put("M_SYSTEMUNYOKIGYOMST", "SYSTM_UNYO_KIGYO_CODE,KSHB");

        primaryKeys.put("T_AREA_MTSH_KKK_SSNKN_TN_C", "NENDO,GROUP_CODE,SSNKN_TNCD");
        primaryKeys.put("T_HNSH_MTSH_KKK_SSNKN_TN_C", "NENDO,GROUP_CODE,SSNKN_TNCD");
        primaryKeys.put("SSNKN_TN_C_CHKST_JSSK", "KANRI_KK_NENDO,TOGO__KUBUN,SSNKN_TNCD,GROUP_CODE");
        primaryKeys.put("SSNKN_TN_C_CHKST_KKK", "KANRI_KK_NENDO,TOGO__KUBUN,SSNKN_TNCD,GROUP_CODE");

        primaryKeys.put("T_ANNOUNCEMENT_MSSG", "MSSG_KUBUN,KSHB");
        primaryKeys.put("T_DWNLD_RRK", "RRK_BANGO");
        TABLE_PRIMARY_KEYS = Collections.unmodifiableMap(primaryKeys);
    }
    
    // 自動生成キーを持つテーブル
    private static final Set<String> AUTO_GENERATED_KEY_TABLES = Set.of("T_DWNLD_RRK", "T_UPLOAD_RRK");
    
    // 挿入されたデータの追跡用
    private final Map<String, List<Map<String, Object>>> insertedDataTracker = new LinkedHashMap<>();


    /**
     * コンストラクタ
     * @param excelFileName Excelファイル名（testdataフォルダ配下）
     */
    public TestDataManager(String excelFileName) {
        this.excelFileName = excelFileName;
    }
    /**
     * Excel ファイルから全テストデータを読み込んでデータベースに挿入
     * 全テーブルのデータ挿入を単一トランザクションで実行し、原子性を保証
     * @return 挿入されたデータの追跡情報
     */
    public Map<String, List<Map<String, Object>>> insertAllTestData() {
        Map<String, List<Map<String, Object>>> backupTracker = new LinkedHashMap<>(insertedDataTracker);

        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream("testdata/" + excelFileName);
             Workbook workbook = new XSSFWorkbook(inputStream)) {

            logger.info("エクスポート用Excel テストデータファイルを読み込み開始: {}", excelFileName);

            // 全テーブルのデータ挿入を単一トランザクションで実行
            LambdaResourceManager.executeWithTransaction(serviceFactory -> {
                // 依存関係順でテーブルデータを挿入
                insertTableDataInTransaction(workbook, "M_SOSHIKIAREAMST", serviceFactory);      // エリアマスタ
                insertTableDataInTransaction(workbook, "M_SK_KKK_AREA", serviceFactory);         // 採算管理単位計画策定エリアマスタ
                insertTableDataInTransaction(workbook, "M_CATEGORYMST", serviceFactory);         // カテゴリマスタ
                insertTableDataInTransaction(workbook, "M_GROUPMST", serviceFactory);            // グループマスタ
                insertTableDataInTransaction(workbook, "M_UNITMST", serviceFactory);             // ユニットマスタ
                insertTableDataInTransaction(workbook, "M_KIGYOMST", serviceFactory);            // 企業マスタ
                insertTableDataInTransaction(workbook, "M_TANTOSHAMST", serviceFactory);         // 担当者マスタ
                insertTableDataInTransaction(workbook, "M_GYT_MEI", serviceFactory);             // 業態名マスタ
                insertTableDataInTransaction(workbook, "M_GYT_KANRI", serviceFactory);           // 業態管理マスタ
                insertTableDataInTransaction(workbook, "M_SAISANKANRITANIMST", serviceFactory);  // 採算管理単位マスタ
                insertTableDataInTransaction(workbook, "M_SAISANKANRITANIFZKKMKMST", serviceFactory); // 採算管理単位付属項目マスタ
                insertTableDataInTransaction(workbook, "T_JINENDO_KKK", serviceFactory);         // 次年度計画マスタ
                insertTableDataInTransaction(workbook, "T_DWNLD_RRK", serviceFactory);           // エクスポート履歴
                insertTableDataInTransaction(workbook, "M_MAKERMST", serviceFactory);           // メーカーマスタ
                insertTableDataInTransaction(workbook, "M_SHAINMST", serviceFactory);           // 社員マスタ
                insertTableDataInTransaction(workbook, "M_SYSTEMUNYOKIGYOMST", serviceFactory);           // システム運用企業マスタ
                insertTableDataInTransaction(workbook, "T_AREA_MTSH_KKK_SSNKN_TN_C", serviceFactory);           // エリア＿見通し・計画_採算管理単位C別
                insertTableDataInTransaction(workbook, "T_HNSH_MTSH_KKK_SSNKN_TN_C", serviceFactory);           // 本社＿見通し・計画_採算管理単位C別
                insertTableDataInTransaction(workbook, "SSNKN_TN_C_CHKST_JSSK", serviceFactory);           // 採算管理単位C別_直接_実績
                insertTableDataInTransaction(workbook, "SSNKN_TN_C_CHKST_KKK", serviceFactory);           // 採算管理単位C別_直接_計画
                // 権限システム関連テーブル（依存関係順）
                insertTableDataInTransaction(workbook, "M_KENGEN", serviceFactory);             // 権限マスタ
                insertTableDataInTransaction(workbook, "M_KEN_RULE", serviceFactory);           // 権限ルールマスタ
                insertTableDataInTransaction(workbook, "M_KEN_PERSON", serviceFactory);         // 個人別権限設定マスタ
                insertTableDataInTransaction(workbook, "T_ANNOUNCEMENT_MSSG", serviceFactory);         // アナウンスメッセージ管理
                return null;
            });

            logger.info("エクスポート用全テストデータの挿入が完了しました");

            return new LinkedHashMap<>(insertedDataTracker);

        } catch (Exception e) {
            logger.error("エクスポート用Excel テストデータ挿入エラー: {}", e.getMessage(), e);

            // トランザクション失敗時はデータベースが自動回滚するため、
            // 追跡情報のみをバックアップ状態に復元
            insertedDataTracker.clear();
            insertedDataTracker.putAll(backupTracker);
            logger.info("トランザクション失敗により、データベースデータは自動回滚されました");

            throw new RuntimeException("エクスポート用Excel テストデータ挿入に失敗しました", e);
        }
    }
    
    /**
     * 特定のテーブルのデータを Excel から読み込んで挿入
     * @param workbook Excel ワークブック
     * @param tableName テーブル名（sheet 名と同じ）
     */
    private void insertTableData(Workbook workbook, String tableName) {
        Sheet sheet = workbook.getSheet(tableName);
        if (sheet == null) {
            logger.warn("Sheet が見つかりません: {}", tableName);
            return;
        }

        List<Map<String, Object>> tableData = readSheetData(sheet);
        if (tableData.isEmpty()) {
            logger.info("テーブル {} にデータがありません", tableName);
            return;
        }

        List<Map<String, Object>> insertedData = insertTableDataToDatabase(tableName, tableData);
        insertedDataTracker.put(tableName, insertedData);

        logger.info("テーブル {} のデータを挿入しました: 件数={}", tableName, insertedData.size());
    }

    /**
     * 特定のテーブルのデータを Excel から読み込んで同一トランザクション内で挿入
     * @param workbook Excel ワークブック
     * @param tableName テーブル名（sheet 名と同じ）
     * @param serviceFactory サービスファクトリ（同一トランザクション内）
     */
    private void insertTableDataInTransaction(Workbook workbook, String tableName, DomainServiceFactory serviceFactory) {
        Sheet sheet = workbook.getSheet(tableName);
        if (sheet == null) {
            logger.warn("Sheet が見つかりません: {}", tableName);
            return;
        }

        List<Map<String, Object>> tableData = readSheetData(sheet);
        if (tableData.isEmpty()) {
            logger.info("テーブル {} にデータがありません", tableName);
            return;
        }

        logger.debug("テーブル {} のデータ挿入を開始します: 件数={}", tableName, tableData.size());

        List<Map<String, Object>> insertedData = new ArrayList<>();
        for (Map<String, Object> row : tableData) {
            Map<String, Object> insertedRow = insertRowToDatabase(tableName, row, serviceFactory);
            insertedData.add(insertedRow);
        }

        // 挿入されたデータを追跡情報に記録
        insertedDataTracker.put(tableName, insertedData);

        logger.info("テーブル {} のデータを挿入しました: 件数={}", tableName, insertedData.size());
    }
    
    /**
     * Sheet からデータを読み込んで Map のリストに変換
     * @param sheet Excel sheet
     * @return データのリスト
     */
    private List<Map<String, Object>> readSheetData(Sheet sheet) {
        List<Map<String, Object>> result = new ArrayList<>();
        
        if (sheet.getPhysicalNumberOfRows() <= 1) {
            return result; // ヘッダーのみまたは空の場合
        }
        
        // ヘッダー行を読み込み
        Row headerRow = sheet.getRow(0);
        List<String> headers = new ArrayList<>();
        for (Cell cell : headerRow) {
            headers.add(getCellValueAsString(cell).trim());
        }
        
        // データ行を読み込み
        for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row dataRow = sheet.getRow(rowIndex);
            if (!isValidRow(dataRow)) continue;
            
            Map<String, Object> rowData = new LinkedHashMap<>();
            for (int colIndex = 0; colIndex < headers.size(); colIndex++) {
                String header = headers.get(colIndex);
                Cell cell = dataRow.getCell(colIndex);
                String value = getCellValueAsString(cell);
                
                // 空文字列は null に変換（データベースの NULL 値として扱う）
                rowData.put(header, value.isEmpty() ? null : value);
            }
            
            result.add(rowData);
        }
        
        return result;
    }
    /**
     * 一行が有効行であるかを判定します（null ではなく、少なくとも 1 つの非空のセルを含みます）
     * @param row 判定対象の行
     * @return 有効行であれば true を返し、そうでなければ false を返します
     */
    private boolean isValidRow(Row row) {
        // 行オブジェクトが null の場合、空行とみなします
        if (row == null) {
            return false;
        }

        // // 行内のすべてのセルをチェックし、有効なデータが存在するかを確認します
        for (int cellNum = row.getFirstCellNum(); cellNum < row.getLastCellNum(); cellNum++) {
            Cell cell = row.getCell(cellNum, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
            String cellValue = getCellValueAsString(cell).trim();

            // 少なくとも 1 つのセルに空白以外の内容がある場合、有効なデータとみなします
            if (!cellValue.isEmpty()) {
                return true;
            }
        }

        // すべてのセルが空の場合、空行とみなします
        return false;
    }
    /**
     * セルの値を文字列として取得
     * @param cell Excel セル
     * @return セルの値（文字列）
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    // 日付セルの場合は YYYYMMDDHHMMSS 形式に変換
                    return String.format("%1$tY%1$tm%1$td%1$tH%1$tM%1$tS", cell.getDateCellValue());
                } else {
                    // 数値セルの場合は整数として扱う
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }
    
    /**
     * テーブルデータをデータベースに挿入
     * @param tableName テーブル名
     * @param tableData テーブルデータ
     * @return 挿入されたデータ（主キー情報付き）
     */
    private List<Map<String, Object>> insertTableDataToDatabase(String tableName, List<Map<String, Object>> tableData) {
        List<Map<String, Object>> insertedData = new ArrayList<>();
        
        try {
            LambdaResourceManager.executeWithTransaction(serviceFactory -> {
                JdbcTemplate jdbcTemplate = serviceFactory.getJdbcTemplate();
                
                for (Map<String, Object> row : tableData) {
                    Map<String, Object> insertedRow = insertRowToDatabase(jdbcTemplate, tableName, row);
                    insertedData.add(insertedRow);
                }
                
                return null;
            });
            
        } catch (Exception e) {
            logger.error("テーブル {} のデータ挿入エラー: {}", tableName, e.getMessage(), e);
            throw new RuntimeException("テーブルデータ挿入に失敗しました: " + tableName, e);
        }
        
        return insertedData;
    }
    
    /**
     * データベースに行を挿入（DomainServiceFactory版）
     * @param tableName テーブル名
     * @param row 行データ
     * @param serviceFactory サービスファクトリ
     * @return 挿入された行データ（主キー情報付き）
     */
    private Map<String, Object> insertRowToDatabase(String tableName, Map<String, Object> row, DomainServiceFactory serviceFactory) {
        JdbcTemplate jdbcTemplate = serviceFactory.getJdbcTemplate();
        return insertRowToDatabase(jdbcTemplate, tableName, row);
    }

    /**
     * データベースに行を挿入
     * @param jdbcTemplate JDBC テンプレート
     * @param tableName テーブル名
     * @param row 行データ
     * @return 挿入された行データ（主キー情報付き）
     */
    private Map<String, Object> insertRowToDatabase(JdbcTemplate jdbcTemplate, String tableName, Map<String, Object> row) {
        // NULL 値を除外してカラムとパラメータを構築
        List<String> columns = new ArrayList<>();
        List<Object> values = new ArrayList<>();
        
        for (Map.Entry<String, Object> entry : row.entrySet()) {
            if (entry.getValue() != null) {
                columns.add(entry.getKey());
                // 数値型字段の型変換処理
                Object convertedValue = convertValueForDatabase(tableName, entry.getKey(), entry.getValue());
                values.add(convertedValue);
            }
        }
        
        // INSERT SQL を動的に生成
        String columnList = String.join(", ", columns);
        String placeholders = String.join(", ", Collections.nCopies(columns.size(), "?"));
        String insertSql = String.format("INSERT INTO %s (%s) VALUES (%s)", tableName, columnList, placeholders);

        logger.debug("データ挿入SQL: テーブル={}, SQL={}, カラム={}, 値={}", tableName, insertSql, columns, values);

        Map<String, Object> insertedRow = new LinkedHashMap<>(row);
        
        try {
            if (AUTO_GENERATED_KEY_TABLES.contains(tableName)) {
                // 自動生成キーを持つテーブルの場合
                // PostgreSQLでは小文字のカラム名を使用（複数パターンを試行）
                Long generatedKey = null;
                String[] keyColumnCandidates = {"rrk_bango", "RRK_BANGO"};

                Exception lastException = null;
                for (String keyColumnName : keyColumnCandidates) {
                    try {
                        generatedKey = jdbcTemplate.insertWithGeneratedKey(insertSql, values.toArray(), keyColumnName);
                        logger.debug("自動生成キー付きでデータを挿入しました: テーブル={}, キー={}, カラム名={}", tableName, generatedKey, keyColumnName);
                        break;
                    } catch (Exception e) {
                        lastException = e;
                        logger.debug("カラム名 {} での挿入に失敗、次のパターンを試行: {} - {}", keyColumnName, e.getClass().getSimpleName(), e.getMessage());

                        // PostgreSQL固有のエラーの場合、詳細情報をログ出力
                        if (e.getMessage().contains("does not exist")) {
                            logger.warn("カラム {} が存在しません。テーブル {} の実際のスキーマを確認してください。", keyColumnName, tableName);
                        }
                    }
                }

                if (generatedKey == null) {
                    throw new RuntimeException("自動生成キーでの挿入に失敗しました: " + tableName, lastException);
                }

                insertedRow.put("RRK_BANGO", generatedKey);
            } else {
                // 通常のテーブルの場合
                jdbcTemplate.update(insertSql, values.toArray());
                logger.debug("データを挿入しました: テーブル={}", tableName);
            }
            
        } catch (Exception e) {
            logger.error("データ挿入エラー: テーブル={}, SQL={}, パラメータ={}", tableName, insertSql, values, e);
            throw new RuntimeException("データ挿入に失敗しました: " + tableName, e);
        }
        
        return insertedRow;
    }



    /**
     * 挿入したテストデータを全て削除
     * @param insertedDataTracker 挿入されたデータの追跡情報
     */
    public void deleteAllTestData(Map<String, List<Map<String, Object>>> insertedDataTracker) {
        if (insertedDataTracker == null || insertedDataTracker.isEmpty()) {
            return;
        }
        
        try {
            // 依存関係の逆順で削除
            deleteTableData("T_DWNLD_RRK", insertedDataTracker.get("T_DWNLD_RRK"));
            deleteTableData("T_JINENDO_KKK", insertedDataTracker.get("T_JINENDO_KKK"));
            deleteTableData("M_SAISANKANRITANIFZKKMKMST", insertedDataTracker.get("M_SAISANKANRITANIFZKKMKMST"));
            deleteTableData("M_SAISANKANRITANIMST", insertedDataTracker.get("M_SAISANKANRITANIMST"));
            deleteTableData("M_GYT_KANRI", insertedDataTracker.get("M_GYT_KANRI"));
            deleteTableData("M_GYT_MEI", insertedDataTracker.get("M_GYT_MEI"));
            deleteTableData("M_TANTOSHAMST", insertedDataTracker.get("M_TANTOSHAMST"));
            deleteTableData("M_KIGYOMST", insertedDataTracker.get("M_KIGYOMST"));
            deleteTableData("M_UNITMST", insertedDataTracker.get("M_UNITMST"));
            deleteTableData("M_GROUPMST", insertedDataTracker.get("M_GROUPMST"));
            deleteTableData("M_CATEGORYMST", insertedDataTracker.get("M_CATEGORYMST"));
            deleteTableData("M_SK_KKK_AREA", insertedDataTracker.get("M_SK_KKK_AREA"));
            deleteTableData("M_SOSHIKIAREAMST", insertedDataTracker.get("M_SOSHIKIAREAMST"));
            deleteTableData("M_MAKERMST", insertedDataTracker.get("M_MAKERMST"));
            deleteTableData("M_SHAINMST", insertedDataTracker.get("M_SHAINMST"));
            deleteTableData("M_SYSTEMUNYOKIGYOMST", insertedDataTracker.get("M_SYSTEMUNYOKIGYOMST"));
            deleteTableData("T_AREA_MTSH_KKK_SSNKN_TN_C", insertedDataTracker.get("T_AREA_MTSH_KKK_SSNKN_TN_C"));
            deleteTableData("T_HNSH_MTSH_KKK_SSNKN_TN_C", insertedDataTracker.get("T_HNSH_MTSH_KKK_SSNKN_TN_C"));
            deleteTableData("SSNKN_TN_C_CHKST_JSSK", insertedDataTracker.get("SSNKN_TN_C_CHKST_JSSK"));
            deleteTableData("SSNKN_TN_C_CHKST_KKK", insertedDataTracker.get("SSNKN_TN_C_CHKST_KKK"));
            deleteTableData("M_KEN_PERSON", insertedDataTracker.get("M_KEN_PERSON"));
            deleteTableData("M_KENGEN", insertedDataTracker.get("M_KENGEN"));
            deleteTableData("M_KEN_RULE", insertedDataTracker.get("M_KEN_RULE"));
            deleteTableData("T_ANNOUNCEMENT_MSSG", insertedDataTracker.get("T_ANNOUNCEMENT_MSSG"));

            logger.info("エクスポート用全テストデータの削除が完了しました");

        } catch (Exception e) {
            logger.warn("エクスポート用テストデータ削除中にエラーが発生しました: {}", e.getMessage());
            // 削除エラーはテスト実行に影響しないため警告レベル
        }
    }

    /**
     * 特定のテーブルのテストデータを削除
     * @param tableName テーブル名
     * @param insertedData 挿入されたデータのリスト
     */
    public static void deleteTableData(String tableName, List<Map<String, Object>> insertedData) {
        if (insertedData == null || insertedData.isEmpty()) {
            return;
        }

        try {
            LambdaResourceManager.executeWithTransaction(serviceFactory -> {
                JdbcTemplate jdbcTemplate = serviceFactory.getJdbcTemplate();

                String primaryKeyColumns = TABLE_PRIMARY_KEYS.get(tableName);
                if (primaryKeyColumns == null) {
                    logger.warn("テーブル {} の主キー情報が見つかりません", tableName);
                    return null;
                }

                for (Map<String, Object> row : insertedData) {
                    deleteRowFromDatabase(jdbcTemplate, tableName, primaryKeyColumns, row);
                }

                logger.info("テーブル {} のテストデータを削除しました: 件数={}", tableName, insertedData.size());

                return null;
            });

        } catch (Exception e) {
            logger.warn("テーブル {} のデータ削除エラー: {}", tableName, e.getMessage());
        }
    }

    /**
     * データベースから行を削除
     * @param jdbcTemplate JDBC テンプレート
     * @param tableName テーブル名
     * @param primaryKeyColumns 主キーカラム（カンマ区切り）
     * @param row 削除対象の行データ
     */
    private static void deleteRowFromDatabase(JdbcTemplate jdbcTemplate, String tableName, String primaryKeyColumns, Map<String, Object> row) {
        String[] keyColumns = primaryKeyColumns.split(",");
        List<String> whereConditions = new ArrayList<>();
        List<Object> whereValues = new ArrayList<>();

        for (String keyColumn : keyColumns) {
            String trimmedColumn = keyColumn.trim();
            Object keyValue = row.get(trimmedColumn);
            if (keyValue != null) {
                whereConditions.add(trimmedColumn + " = ?");
                whereValues.add(convertValueForDatabase(tableName, trimmedColumn,keyValue));
            }
        }

        if (whereConditions.isEmpty()) {
            logger.warn("削除条件が構築できません: テーブル={}, 行データ={}", tableName, row);
            return;
        }

        String deleteSql = String.format("DELETE FROM %s WHERE %s", tableName, String.join(" AND ", whereConditions));

        try {
            int deletedRows = jdbcTemplate.update(deleteSql, whereValues.toArray());
            logger.debug("データを削除しました: テーブル={}, 削除件数={}", tableName, deletedRows);
        } catch (Exception e) {
            logger.warn("データ削除エラー: テーブル={}, SQL={}, パラメータ={}", tableName, deleteSql, whereValues, e);
            throw new RuntimeException("データ削除に失敗しました: " + tableName, e);
        }
    }

    /**
     * データベース挿入用の値変換処理
     * 数値型字段を適切な型に変換
     *
     * @param tableName テーブル名
     * @param columnName カラム名
     * @param value 元の値
     * @return 変換後の値
     */
    private static Object convertValueForDatabase(String tableName, String columnName, Object value) {
        if (value == null) {
            return null;
        }

        String stringValue = value.toString();

        // M_SOSHIKIAREAMST表の数値型字段処理
        if ("M_SOSHIKIAREAMST".equals(tableName)) {
            if ("AREA_SHTSR".equals(columnName) || "SUB_AREA_SHTSR".equals(columnName) ) {
                try {
                    return Integer.parseInt(stringValue);
                } catch (NumberFormatException e) {
                    logger.warn("数値変換エラー: テーブル={}, カラム={}, 値={}", tableName, columnName, stringValue);
                    return stringValue; // 変換失敗時は元の値を返す
                }
            }
            if ("VRSN".equals(columnName)) {
                try {
                    return Integer.parseInt(stringValue);
                } catch (NumberFormatException e) {
                    logger.warn("数値変換エラー: テーブル={}, カラム={}, 値={}", tableName, columnName, stringValue);
                    return stringValue;
                }
            }
        }

        if ("M_SAISANKANRITANIMST".equals(tableName)) {
            if ("EOS_SNDN_NO_KTS".equals(columnName)|| "TGK_SNDN_NO_KTS".equals(columnName)|| "SENYO_DMPY_GYOSU".equals(columnName)) {
                try {
                    return Integer.parseInt(stringValue);
                } catch (NumberFormatException e) {
                    logger.warn("数値変換エラー: テーブル={}, カラム={}, 値={}", tableName, columnName, stringValue);
                    return stringValue; // 変換失敗時は元の値を返す
                }
            }
        }

        // M_SK_KKK_AREA表の数値型字段処理
        if ("M_SK_KKK_AREA".equals(tableName)) {
            if ("AREA_ORDER".equals(columnName) || "VRSN".equals(columnName)) {
                try {
                    return Integer.parseInt(stringValue);
                } catch (NumberFormatException e) {
                    logger.warn("数値変換エラー: テーブル={}, カラム={}, 値={}", tableName, columnName, stringValue);
                    return stringValue;
                }
            }
        }

        // その他のテーブルの数値型字段処理（必要に応じて追加）
        // VRSN字段は多くのテーブルで共通
        if ("VRSN".equals(columnName)) {
            try {
                return Integer.parseInt(stringValue);
            } catch (NumberFormatException e) {
                logger.warn("数値変換エラー: テーブル={}, カラム={}, 値={}", tableName, columnName, stringValue);
                return stringValue;
            }
        }

        // T_AREA_MTSH_KKK_SSNKN_TN_C表の数値型字段処理
        if ("T_AREA_MTSH_KKK_SSNKN_TN_C".equals(tableName)) {
            if (T_AREA_MTSH_KKK_SSNKN_TN_C_NUMERIC.contains(columnName)) {
                try {
                    return new BigDecimal(stringValue);
                } catch (NumberFormatException e) {
                    logger.warn("数値変換エラー: テーブル={}, カラム={}, 値={}", tableName, columnName, stringValue);
                    return stringValue;
                }
            }
        }

        // M_SOSHIKIAREAMST 表の数値型字段処理
        if ("M_SOSHIKIAREAMST".equals(tableName)) {
            List<String> numericColList = Arrays.asList("AREA_SHTSR", "SUB_AREA_SHTSR");
            if (numericColList.contains(columnName)) {
                try {
                    return new BigDecimal(stringValue);
                } catch (NumberFormatException e) {
                    logger.warn("数値変換エラー: テーブル={}, カラム={}, 値={}", tableName, columnName, stringValue);
                    return stringValue;
                }
            }
        }

        // M_SAISANKANRITANIMST 表の数値型字段処理
        if ("M_SAISANKANRITANIMST".equals(tableName)) {
            List<String> numericColList = Arrays.asList("SENYO_DMPY_GYOSU", "TGK_SNDN_NO_KTS", "EOS_SNDN_NO_KTS");
            if (numericColList.contains(columnName)) {
                try {
                    return new BigDecimal(stringValue);
                } catch (NumberFormatException e) {
                    logger.warn("数値変換エラー: テーブル={}, カラム={}, 値={}", tableName, columnName, stringValue);
                    return stringValue;
                }
            }
        }

        // T_HNSH_MTSH_KKK_SSNKN_TN_C表の数値型字段処理
        if ("T_HNSH_MTSH_KKK_SSNKN_TN_C".equals(tableName)) {
            if (T_HNSH_MTSH_KKK_SSNKN_TN_C_NUMERIC.contains(columnName)) {
                try {
                    return new BigDecimal(stringValue);
                } catch (NumberFormatException e) {
                    logger.warn("数値変換エラー: テーブル={}, カラム={}, 値={}", tableName, columnName, stringValue);
                    return stringValue;
                }
            }
        }

        // SSNKN_TN_C_CHKST_JSSK表の数値型字段処理
        if ("SSNKN_TN_C_CHKST_JSSK".equals(tableName)) {
            if (SSNKN_TN_C_CHKST_JSSK_NUMERIC.contains(columnName)) {
                try {
                    return new BigDecimal(stringValue);
                } catch (NumberFormatException e) {
                    logger.warn("数値変換エラー: テーブル={}, カラム={}, 値={}", tableName, columnName, stringValue);
                    return stringValue;
                }
            }

        }

        // SSNKN_TN_C_CHKST_JSSK表の数値型字段処理
        if ("SSNKN_TN_C_CHKST_KKK".equals(tableName)) {
            if (SSNKN_TN_C_CHKST_KKK_NUMERIC.contains(columnName)) {
                try {
                    return new BigDecimal(stringValue);
                } catch (NumberFormatException e) {
                    logger.warn("数値変換エラー: テーブル={}, カラム={}, 値={}", tableName, columnName, stringValue);
                    return stringValue;
                }
            }

        }

        // T_ANNOUNCEMENT_MSSG表の数値型字段処理
        if ("T_ANNOUNCEMENT_MSSG".equals(tableName)) {
            if ("MSSG_ID".equals(columnName)) {
                try {
                    return Integer.parseInt(stringValue);
                } catch (NumberFormatException e) {
                    logger.warn("数値変換エラー: テーブル={}, カラム={}, 値={}", tableName, columnName, stringValue);
                    return stringValue;
                }
            }
        }

        return value; // 変換不要な場合は元の値を返す
    }


    /** 本社＿見通し・計画_採算管理単位C別テーブル_列_NUMERIC*/
    private static final List<String> T_HNSH_MTSH_KKK_SSNKN_TN_C_NUMERIC = Arrays.asList(
            "GYT_HRTS", //業態比率
            "KKK_ZAIKO_URG_1_TSKM", //計画＿在庫売上＿１月目
            "KKK_CHKS_URG_1_TSKM", //計画＿直送売上＿１月目
            "KKK_ZAIKO_RIEKI_1_TSKM", //計画＿在庫利益＿１月目
            "KKK_CHKS_RIEKI_1_TSKM", //計画＿直送利益＿１月目
            "KKK_ZAIKO_HMPNT_1_TSKM", //計画＿在庫返品等＿１月目
            "KKK_CHKS_HMPNT_1_TSKM", //計画＿直送返品等＿１月目
            "KKK_ZAIKO_SHHR_RBT_1_TSKM", //計画＿在庫支払リベート＿１月目
            "KKK_CHKS_SHHR_RBT_1_TSKM", //計画＿直送支払リベート＿１月目
            "KKK_ZAIKO_CNTR_FEE_1_TSKM", //計画＿在庫センターフィ＿１月目
            "KKK_CHKS_CNTR_FEE_1_TSKM", //計画＿直送センターフィ＿１月目
            "KKK_ZAIKO_URG_2_TSKM", //計画＿在庫売上＿２月目
            "KKK_CHKS_URG_2_TSKM", //計画＿直送売上＿２月目
            "KKK_ZAIKO_RIEKI_2_TSKM", //計画＿在庫利益＿２月目
            "KKK_CHKS_RIEKI_2_TSKM", //計画＿直送利益＿２月目
            "KKK_ZAIKO_HMPNT_2_TSKM", //計画＿在庫返品等＿２月目
            "KKK_CHKS_HMPNT_2_TSKM", //計画＿直送返品等＿２月目
            "KKK_ZAIKO_SHHR_RBT_2_TSKM", //計画＿在庫支払リベート＿２月目
            "KKK_CHKS_SHHR_RBT_2_TSKM", //計画＿直送支払リベート＿２月目
            "KKK_ZAIKO_CNTR_FEE_2_TSKM", //計画＿在庫センターフィ＿２月目
            "KKK_CHKS_CNTR_FEE_2_TSKM", //計画＿直送センターフィ＿２月目
            "KKK_ZAIKO_URG_3_TSKM", //計画＿在庫売上＿３月目
            "KKK_CHKS_URG_3_TSKM", //計画＿直送売上＿３月目
            "KKK_ZAIKO_RIEKI_3_TSKM", //計画＿在庫利益＿３月目
            "KKK_CHKS_RIEKI_3_TSKM", //計画＿直送利益＿３月目
            "KKK_ZAIKO_HMPNT_3_TSKM", //計画＿在庫返品等＿３月目
            "KKK_CHKS_HMPNT_3_TSKM", //計画＿直送返品等＿３月目
            "KKK_ZAIKO_SHHR_RBT_3_TSKM", //計画＿在庫支払リベート＿３月目
            "KKK_CHKS_SHHR_RBT_3_TSKM", //計画＿直送支払リベート＿３月目
            "KKK_ZAIKO_CNTR_FEE_3_TSKM", //計画＿在庫センターフィ＿３月目
            "KKK_CHKS_CNTR_FEE_3_TSKM", //計画＿直送センターフィ＿３月目
            "KKK_ZAIKO_URG_4_TSKM", //計画＿在庫売上＿４月目
            "KKK_CHKS_URG_4_TSKM", //計画＿直送売上＿４月目
            "KKK_ZAIKO_RIEKI_4_TSKM", //計画＿在庫利益＿４月目
            "KKK_CHKS_RIEKI_4_TSKM", //計画＿直送利益＿４月目
            "KKK_ZAIKO_HMPNT_4_TSKM", //計画＿在庫返品等＿４月目
            "KKK_CHKS_HMPNT_4_TSKM", //計画＿直送返品等＿４月目
            "KKK_ZAIKO_SHHR_RBT_4_TSKM", //計画＿在庫支払リベート＿４月目
            "KKK_CHKS_SHHR_RBT_4_TSKM", //計画＿直送支払リベート＿４月目
            "KKK_ZAIKO_CNTR_FEE_4_TSKM", //計画＿在庫センターフィ＿４月目
            "KKK_CHKS_CNTR_FEE_4_TSKM", //計画＿直送センターフィ＿４月目
            "KKK_ZAIKO_URG_5_TSKM", //計画＿在庫売上＿５月目
            "KKK_CHKS_URG_5_TSKM", //計画＿直送売上＿５月目
            "KKK_ZAIKO_RIEKI_5_TSKM", //計画＿在庫利益＿５月目
            "KKK_CHKS_RIEKI_5_TSKM", //計画＿直送利益＿５月目
            "KKK_ZAIKO_HMPNT_5_TSKM", //計画＿在庫返品等＿５月目
            "KKK_CHKS_HMPNT_5_TSKM", //計画＿直送返品等＿５月目
            "KKK_ZAIKO_SHHR_RBT_5_TSKM", //計画＿在庫支払リベート＿５月目
            "KKK_CHKS_SHHR_RBT_5_TSKM", //計画＿直送支払リベート＿５月目
            "KKK_ZAIKO_CNTR_FEE_5_TSKM", //計画＿在庫センターフィ＿５月目
            "KKK_CHKS_CNTR_FEE_5_TSKM", //計画＿直送センターフィ＿５月目
            "KKK_ZAIKO_URG_6_TSKM", //計画＿在庫売上＿６月目
            "KKK_CHKS_URG_6_TSKM", //計画＿直送売上＿６月目
            "KKK_ZAIKO_RIEKI_6_TSKM", //計画＿在庫利益＿６月目
            "KKK_CHKS_RIEKI_6_TSKM", //計画＿直送利益＿６月目
            "KKK_ZAIKO_HMPNT_6_TSKM", //計画＿在庫返品等＿６月目
            "KKK_CHKS_HMPNT_6_TSKM", //計画＿直送返品等＿６月目
            "KKK_ZAIKO_SHHR_RBT_6_TSKM", //計画＿在庫支払リベート＿６月目
            "KKK_CHKS_SHHR_RBT_6_TSKM", //計画＿直送支払リベート＿６月目
            "KKK_ZAIKO_CNTR_FEE_6_TSKM", //計画＿在庫センターフィ＿６月目
            "KKK_CHKS_CNTR_FEE_6_TSKM", //計画＿直送センターフィ＿６月目
            "KKK_ZAIKO_URG_7_TSKM", //計画＿在庫売上＿７月目
            "KKK_CHKS_URG_7_TSKM", //計画＿直送売上＿７月目
            "KKK_ZAIKO_RIEKI_7_TSKM", //計画＿在庫利益＿７月目
            "KKK_CHKS_RIEKI_7_TSKM", //計画＿直送利益＿７月目
            "KKK_ZAIKO_HMPNT_7_TSKM", //計画＿在庫返品等＿７月目
            "KKK_CHKS_HMPNT_7_TSKM", //計画＿直送返品等＿７月目
            "KKK_ZAIKO_SHHR_RBT_7_TSKM", //計画＿在庫支払リベート＿７月目
            "KKK_CHKS_SHHR_RBT_7_TSKM", //計画＿直送支払リベート＿７月目
            "KKK_ZAIKO_CNTR_FEE_7_TSKM", //計画＿在庫センターフィ＿７月目
            "KKK_CHKS_CNTR_FEE_7_TSKM", //計画＿直送センターフィ＿７月目
            "KKK_ZAIKO_URG_8_TSKM", //計画＿在庫売上＿８月目
            "KKK_CHKS_URG_8_TSKM", //計画＿直送売上＿８月目
            "KKK_ZAIKO_RIEKI_8_TSKM", //計画＿在庫利益＿８月目
            "KKK_CHKS_RIEKI_8_TSKM", //計画＿直送利益＿８月目
            "KKK_ZAIKO_HMPNT_8_TSKM", //計画＿在庫返品等＿８月目
            "KKK_CHKS_HMPNT_8_TSKM", //計画＿直送返品等＿８月目
            "KKK_ZAIKO_SHHR_RBT_8_TSKM", //計画＿在庫支払リベート＿８月目
            "KKK_CHKS_SHHR_RBT_8_TSKM", //計画＿直送支払リベート＿８月目
            "KKK_ZAIKO_CNTR_FEE_8_TSKM", //計画＿在庫センターフィ＿８月目
            "KKK_CHKS_CNTR_FEE_8_TSKM", //計画＿直送センターフィ＿８月目
            "KKK_ZAIKO_URG_9_TSKM", //計画＿在庫売上＿９月目
            "KKK_CHKS_URG_9_TSKM", //計画＿直送売上＿９月目
            "KKK_ZAIKO_RIEKI_9_TSKM", //計画＿在庫利益＿９月目
            "KKK_CHKS_RIEKI_9_TSKM", //計画＿直送利益＿９月目
            "KKK_ZAIKO_HMPNT_9_TSKM", //計画＿在庫返品等＿９月目
            "KKK_CHKS_HMPNT_9_TSKM", //計画＿直送返品等＿９月目
            "KKK_ZAIKO_SHHR_RBT_9_TSKM", //計画＿在庫支払リベート＿９月目
            "KKK_CHKS_SHHR_RBT_9_TSKM", //計画＿直送支払リベート＿９月目
            "KKK_ZAIKO_CNTR_FEE_9_TSKM", //計画＿在庫センターフィ＿９月目
            "KKK_CHKS_CNTR_FEE_9_TSKM", //計画＿直送センターフィ＿９月目
            "KKK_ZAIKO_URG_10_TSKM", //計画＿在庫売上＿１０月目
            "KKK_CHKS_URG_10_TSKM", //計画＿直送売上＿１０月目
            "KKK_ZAIKO_RIEKI_10_TSKM", //計画＿在庫利益＿１０月目
            "KKK_CHKS_RIEKI_10_TSKM", //計画＿直送利益＿１０月目
            "KKK_ZAIKO_HMPNT_10_TSKM", //計画＿在庫返品等＿１０月目
            "KKK_CHKS_HMPNT_10_TSKM", //計画＿直送返品等＿１０月目
            "KKK_ZAIKO_SHHR_RBT_10_TSKM", //計画＿在庫支払リベート＿１０月目
            "KKK_CHKS_SHHR_RBT_10_TSKM", //計画＿直送支払リベート＿１０月目
            "KKK_ZAIKO_CNTR_FEE_10_TSKM", //計画＿在庫センターフィ＿１０月目
            "KKK_CHKS_CNTR_FEE_10_TSKM", //計画＿直送センターフィ＿１０月目
            "KKK_ZAIKO_URG_11_TSKM", //計画＿在庫売上＿１１月目
            "KKK_CHKS_URG_11_TSKM", //計画＿直送売上＿１１月目
            "KKK_ZAIKO_RIEKI_11_TSKM", //計画＿在庫利益＿１１月目
            "KKK_CHKS_RIEKI_11_TSKM", //計画＿直送利益＿１１月目
            "KKK_ZAIKO_HMPNT_11_TSKM", //計画＿在庫返品等＿１１月目
            "KKK_CHKS_HMPNT_11_TSKM", //計画＿直送返品等＿１１月目
            "KKK_ZAIKO_SHHR_RBT_11_TSKM", //計画＿在庫支払リベート＿１１月目
            "KKK_CHKS_SHHR_RBT_11_TSKM", //計画＿直送支払リベート＿１１月目
            "KKK_ZAIKO_CNTR_FEE_11_TSKM", //計画＿在庫センターフィ＿１１月目
            "KKK_CHKS_CNTR_FEE_11_TSKM", //計画＿直送センターフィ＿１１月目
            "KKK_ZAIKO_URG_12_TSKM", //計画＿在庫売上＿１２月目
            "KKK_CHKS_URG_12_TSKM", //計画＿直送売上＿１２月目
            "KKK_ZAIKO_RIEKI_12_TSKM", //計画＿在庫利益＿１２月目
            "KKK_CHKS_RIEKI_12_TSKM", //計画＿直送利益＿１２月目
            "KKK_ZAIKO_HMPNT_12_TSKM", //計画＿在庫返品等＿１２月目
            "KKK_CHKS_HMPNT_12_TSKM", //計画＿直送返品等＿１２月目
            "KKK_ZAIKO_SHHR_RBT_12_TSKM", //計画＿在庫支払リベート＿１２月目
            "KKK_CHKS_SHHR_RBT_12_TSKM", //計画＿直送支払リベート＿１２月目
            "KKK_ZAIKO_CNTR_FEE_12_TSKM", //計画＿在庫センターフィ＿１２月目
            "KKK_CHKS_CNTR_FEE_12_TSKM", //計画＿直送センターフィ＿１２月目
            "JSSK_MTSH_ZAIKO_URG_10_TSKM", //実績見通し＿在庫売上＿１０月目
            "JSSK_MTSH_CHKS_URG_10_TSKM", //実績見通し＿直送売上＿１０月目
            "JSSK_MTSH_ZAIKO_RIEKI_10_TSKM", //実績見通し＿在庫利益＿１０月目
            "JSSK_MTSH_CHKS_RIEKI_10_TSKM", //実績見通し＿直送利益＿１０月目
            "JSSK_MTSH_ZAIKO_HMPNT_10_TSKM", //実績見通し＿在庫返品等＿１０月目
            "JSSK_MTSH_CHKS_HMPNT_10_TSKM", //実績見通し＿直送返品等＿１０月目
            "JSSK_MTSH_ZAIKO_SHHR_RBT_10_TSKM", //実績見通し＿在庫支払リベート＿１０月目
            "JSSK_MTSH_CHKS_SHHR_RBT_10_TSKM", //実績見通し＿直送支払リベート＿１０月目
            "JSSK_MTSH_ZAIKO_CNTR_FEE_10_TSKM", //実績見通し＿在庫センターフィ＿１０月目
            "JSSK_MTSH_CHKS_CNTR_FEE_10_TSKM", //実績見通し＿直送センターフィ＿１０月目
            "JSSK_MTSH_ZAIKO_URG_11_TSKM", //実績見通し＿在庫売上＿１１月目
            "JSSK_MTSH_CHKS_URG_11_TSKM", //実績見通し＿直送売上＿１１月目
            "JSSK_MTSH_ZAIKO_RIEKI_11_TSKM", //実績見通し＿在庫利益＿１１月目
            "JSSK_MTSH_CHKS_RIEKI_11_TSKM", //実績見通し＿直送利益＿１１月目
            "JSSK_MTSH_ZAIKO_HMPNT_11_TSKM", //実績見通し＿在庫返品等＿１１月目
            "JSSK_MTSH_CHKS_HMPNT_11_TSKM", //実績見通し＿直送返品等＿１１月目
            "JSSK_MTSH_ZAIKO_SHHR_RBT_11_TSKM", //実績見通し＿在庫支払リベート＿１１月目
            "JSSK_MTSH_CHKS_SHHR_RBT_11_TSKM", //実績見通し＿直送支払リベート＿１１月目
            "JSSK_MTSH_ZAIKO_CNTR_FEE_11_TSKM", //実績見通し＿在庫センターフィ＿１１月目
            "JSSK_MTSH_CHKS_CNTR_FEE_11_TSKM", //実績見通し＿直送センターフィ＿１１月目
            "JSSK_MTSH_ZAIKO_URG_12_TSKM", //実績見通し＿在庫売上＿１２月目
            "JSSK_MTSH_CHKS_URG_12_TSKM", //実績見通し＿直送売上＿１２月目
            "JSSK_MTSH_ZAIKO_RIEKI_12_TSKM", //実績見通し＿在庫利益＿１２月目
            "JSSK_MTSH_CHKS_RIEKI_12_TSKM", //実績見通し＿直送利益＿１２月目
            "JSSK_MTSH_ZAIKO_HMPNT_12_TSKM", //実績見通し＿在庫返品等＿１２月目
            "JSSK_MTSH_CHKS_HMPNT_12_TSKM", //実績見通し＿直送返品等＿１２月目
            "JSSK_MTSH_ZAIKO_SHHR_RBT_12_TSKM", //実績見通し＿在庫支払リベート＿１２月目
            "JSSK_MTSH_CHKS_SHHR_RBT_12_TSKM", //実績見通し＿直送支払リベート＿１２月目
            "JSSK_MTSH_ZAIKO_CNTR_FEE_12_TSKM", //実績見通し＿在庫センターフィ＿１２月目
            "JSSK_MTSH_CHKS_CNTR_FEE_12_TSKM" //実績見通し＿直送センターフィ＿１２月目
    );

    /** 採算管理単位C別_直接_実績テーブル_列_NUMERIC*/
    private static final List<String> SSNKN_TN_C_CHKST_JSSK_NUMERIC = Arrays.asList(
//            "KANRI_KK_NENDO", //管理会計年度
            "JSSK_1_TSKM", //１月目
            "JSSK_2_TSKM", //２月目
            "JSSK_3_TSKM", //３月目
            "JSSK_4_TSKM", //４月目
            "JSSK_5_TSKM", //５月目
            "JSSK_6_TSKM", //６月目
            "JSSK_7_TSKM", //７月目
            "JSSK_8_TSKM", //８月目
            "JSSK_9_TSKM", //９月目
            "JSSK_10_TSKM", //１０月目
            "JSSK_11_TSKM", //１１月目
            "JSSK_12_TSKM", //１２月目
            "JSSK_GK" //合計
    );


    private static final List<String> SSNKN_TN_C_CHKST_KKK_NUMERIC = Arrays.asList(
            "KANRI_KK_NENDO", //管理会計年度
            "KKK_1_TSKM", //１月目
            "KKK_2_TSKM", //２月目
            "KKK_3_TSKM", //３月目
            "KKK_4_TSKM", //４月目
            "KKK_5_TSKM", //５月目
            "KKK_6_TSKM", //６月目
            "KKK_7_TSKM", //７月目
            "KKK_8_TSKM", //８月目
            "KKK_9_TSKM", //９月目
            "KKK_10_TSKM", //１０月目
            "KKK_11_TSKM", //１１月目
            "KKK_12_TSKM", //１２月目
            "KKK_GK" //合計
    );

    /** エリア＿見通し・計画_採算管理単位C別テーブル_列_NUMERIC*/
    private static final List<String> T_AREA_MTSH_KKK_SSNKN_TN_C_NUMERIC = Arrays.asList(
            "GYT_HRTS", // 業態比率
            "KKK_ZAIKO_URG_1_TSKM", // 計画＿在庫売上＿１月目
            "KKK_CHKS_URG_1_TSKM", // 計画＿直送売上＿１月目
            "KKK_ZAIKO_RIEKI_1_TSKM", // 計画＿在庫利益＿１月目
            "KKK_CHKS_RIEKI_1_TSKM", // 計画＿直送利益＿１月目
            "KKK_ZAIKO_HMPNT_1_TSKM", // 計画＿在庫返品等＿１月目
            "KKK_CHKS_HMPNT_1_TSKM", // 計画＿直送返品等＿１月目
            "KKK_ZAIKO_SHHR_RBT_1_TSKM", // 計画＿在庫支払リベート＿１月目
            "KKK_CHKS_SHHR_RBT_1_TSKM", // 計画＿直送支払リベート＿１月目
            "KKK_ZAIKO_CNTR_FEE_1_TSKM", // 計画＿在庫センターフィ＿１月目
            "KKK_CHKS_CNTR_FEE_1_TSKM", // 計画＿直送センターフィ＿１月目
            "KKK_ZAIKO_URG_2_TSKM", // 計画＿在庫売上＿２月目
            "KKK_CHKS_URG_2_TSKM", // 計画＿直送売上＿２月目
            "KKK_ZAIKO_RIEKI_2_TSKM", // 計画＿在庫利益＿２月目
            "KKK_CHKS_RIEKI_2_TSKM", // 計画＿直送利益＿２月目
            "KKK_ZAIKO_HMPNT_2_TSKM", // 計画＿在庫返品等＿２月目
            "KKK_CHKS_HMPNT_2_TSKM", // 計画＿直送返品等＿２月目
            "KKK_ZAIKO_SHHR_RBT_2_TSKM", // 計画＿在庫支払リベート＿２月目
            "KKK_CHKS_SHHR_RBT_2_TSKM", // 計画＿直送支払リベート＿２月目
            "KKK_ZAIKO_CNTR_FEE_2_TSKM", // 計画＿在庫センターフィ＿２月目
            "KKK_CHKS_CNTR_FEE_2_TSKM", // 計画＿直送センターフィ＿２月目
            "KKK_ZAIKO_URG_3_TSKM", // 計画＿在庫売上＿３月目
            "KKK_CHKS_URG_3_TSKM", // 計画＿直送売上＿３月目
            "KKK_ZAIKO_RIEKI_3_TSKM", // 計画＿在庫利益＿３月目
            "KKK_CHKS_RIEKI_3_TSKM", // 計画＿直送利益＿３月目
            "KKK_ZAIKO_HMPNT_3_TSKM", // 計画＿在庫返品等＿３月目
            "KKK_CHKS_HMPNT_3_TSKM", // 計画＿直送返品等＿３月目
            "KKK_ZAIKO_SHHR_RBT_3_TSKM", // 計画＿在庫支払リベート＿３月目
            "KKK_CHKS_SHHR_RBT_3_TSKM", // 計画＿直送支払リベート＿３月目
            "KKK_ZAIKO_CNTR_FEE_3_TSKM", // 計画＿在庫センターフィ＿３月目
            "KKK_CHKS_CNTR_FEE_3_TSKM", // 計画＿直送センターフィ＿３月目
            "KKK_ZAIKO_URG_4_TSKM", // 計画＿在庫売上＿４月目
            "KKK_CHKS_URG_4_TSKM", // 計画＿直送売上＿４月目
            "KKK_ZAIKO_RIEKI_4_TSKM", // 計画＿在庫利益＿４月目
            "KKK_CHKS_RIEKI_4_TSKM", // 計画＿直送利益＿４月目
            "KKK_ZAIKO_HMPNT_4_TSKM", // 計画＿在庫返品等＿４月目
            "KKK_CHKS_HMPNT_4_TSKM", // 計画＿直送返品等＿４月目
            "KKK_ZAIKO_SHHR_RBT_4_TSKM", // 計画＿在庫支払リベート＿４月目
            "KKK_CHKS_SHHR_RBT_4_TSKM", // 計画＿直送支払リベート＿４月目
            "KKK_ZAIKO_CNTR_FEE_4_TSKM", // 計画＿在庫センターフィ＿４月目
            "KKK_CHKS_CNTR_FEE_4_TSKM", // 計画＿直送センターフィ＿４月目
            "KKK_ZAIKO_URG_5_TSKM", // 計画＿在庫売上＿５月目
            "KKK_CHKS_URG_5_TSKM", // 計画＿直送売上＿５月目
            "KKK_ZAIKO_RIEKI_5_TSKM", // 計画＿在庫利益＿５月目
            "KKK_CHKS_RIEKI_5_TSKM", // 計画＿直送利益＿５月目
            "KKK_ZAIKO_HMPNT_5_TSKM", // 計画＿在庫返品等＿５月目
            "KKK_CHKS_HMPNT_5_TSKM", // 計画＿直送返品等＿５月目
            "KKK_ZAIKO_SHHR_RBT_5_TSKM", // 計画＿在庫支払リベート＿５月目
            "KKK_CHKS_SHHR_RBT_5_TSKM", // 計画＿直送支払リベート＿５月目
            "KKK_ZAIKO_CNTR_FEE_5_TSKM", // 計画＿在庫センターフィ＿５月目
            "KKK_CHKS_CNTR_FEE_5_TSKM", // 計画＿直送センターフィ＿５月目
            "KKK_ZAIKO_URG_6_TSKM", // 計画＿在庫売上＿６月目
            "KKK_CHKS_URG_6_TSKM", // 計画＿直送売上＿６月目
            "KKK_ZAIKO_RIEKI_6_TSKM", // 計画＿在庫利益＿６月目
            "KKK_CHKS_RIEKI_6_TSKM", // 計画＿直送利益＿６月目
            "KKK_ZAIKO_HMPNT_6_TSKM", // 計画＿在庫返品等＿６月目
            "KKK_CHKS_HMPNT_6_TSKM", // 計画＿直送返品等＿６月目
            "KKK_ZAIKO_SHHR_RBT_6_TSKM", // 計画＿在庫支払リベート＿６月目
            "KKK_CHKS_SHHR_RBT_6_TSKM", // 計画＿直送支払リベート＿６月目
            "KKK_ZAIKO_CNTR_FEE_6_TSKM", // 計画＿在庫センターフィ＿６月目
            "KKK_CHKS_CNTR_FEE_6_TSKM", // 計画＿直送センターフィ＿６月目
            "KKK_ZAIKO_URG_7_TSKM", // 計画＿在庫売上＿７月目
            "KKK_CHKS_URG_7_TSKM", // 計画＿直送売上＿７月目
            "KKK_ZAIKO_RIEKI_7_TSKM", // 計画＿在庫利益＿７月目
            "KKK_CHKS_RIEKI_7_TSKM", // 計画＿直送利益＿７月目
            "KKK_ZAIKO_HMPNT_7_TSKM", // 計画＿在庫返品等＿７月目
            "KKK_CHKS_HMPNT_7_TSKM", // 計画＿直送返品等＿７月目
            "KKK_ZAIKO_SHHR_RBT_7_TSKM", // 計画＿在庫支払リベート＿７月目
            "KKK_CHKS_SHHR_RBT_7_TSKM", // 計画＿直送支払リベート＿７月目
            "KKK_ZAIKO_CNTR_FEE_7_TSKM", // 計画＿在庫センターフィ＿７月目
            "KKK_CHKS_CNTR_FEE_7_TSKM", // 計画＿直送センターフィ＿７月目
            "KKK_ZAIKO_URG_8_TSKM", // 計画＿在庫売上＿８月目
            "KKK_CHKS_URG_8_TSKM", // 計画＿直送売上＿８月目
            "KKK_ZAIKO_RIEKI_8_TSKM", // 計画＿在庫利益＿８月目
            "KKK_CHKS_RIEKI_8_TSKM", // 計画＿直送利益＿８月目
            "KKK_ZAIKO_HMPNT_8_TSKM", // 計画＿在庫返品等＿８月目
            "KKK_CHKS_HMPNT_8_TSKM", // 計画＿直送返品等＿８月目
            "KKK_ZAIKO_SHHR_RBT_8_TSKM", // 計画＿在庫支払リベート＿８月目
            "KKK_CHKS_SHHR_RBT_8_TSKM", // 計画＿直送支払リベート＿８月目
            "KKK_ZAIKO_CNTR_FEE_8_TSKM", // 計画＿在庫センターフィ＿８月目
            "KKK_CHKS_CNTR_FEE_8_TSKM", // 計画＿直送センターフィ＿８月目
            "KKK_ZAIKO_URG_9_TSKM", // 計画＿在庫売上＿９月目
            "KKK_CHKS_URG_9_TSKM", // 計画＿直送売上＿９月目
            "KKK_ZAIKO_RIEKI_9_TSKM", // 計画＿在庫利益＿９月目
            "KKK_CHKS_RIEKI_9_TSKM", // 計画＿直送利益＿９月目
            "KKK_ZAIKO_HMPNT_9_TSKM", // 計画＿在庫返品等＿９月目
            "KKK_CHKS_HMPNT_9_TSKM", // 計画＿直送返品等＿９月目
            "KKK_ZAIKO_SHHR_RBT_9_TSKM", // 計画＿在庫支払リベート＿９月目
            "KKK_CHKS_SHHR_RBT_9_TSKM", // 計画＿直送支払リベート＿９月目
            "KKK_ZAIKO_CNTR_FEE_9_TSKM", // 計画＿在庫センターフィ＿９月目
            "KKK_CHKS_CNTR_FEE_9_TSKM", // 計画＿直送センターフィ＿９月目
            "KKK_ZAIKO_URG_10_TSKM", // 計画＿在庫売上＿１０月目
            "KKK_CHKS_URG_10_TSKM", // 計画＿直送売上＿１０月目
            "KKK_ZAIKO_RIEKI_10_TSKM", // 計画＿在庫利益＿１０月目
            "KKK_CHKS_RIEKI_10_TSKM", // 計画＿直送利益＿１０月目
            "KKK_ZAIKO_HMPNT_10_TSKM", // 計画＿在庫返品等＿１０月目
            "KKK_CHKS_HMPNT_10_TSKM", // 計画＿直送返品等＿１０月目
            "KKK_ZAIKO_SHHR_RBT_10_TSKM", // 計画＿在庫支払リベート＿１０月目
            "KKK_CHKS_SHHR_RBT_10_TSKM", // 計画＿直送支払リベート＿１０月目
            "KKK_ZAIKO_CNTR_FEE_10_TSKM", // 計画＿在庫センターフィ＿１０月目
            "KKK_CHKS_CNTR_FEE_10_TSKM", // 計画＿直送センターフィ＿１０月目
            "KKK_ZAIKO_URG_11_TSKM", // 計画＿在庫売上＿１１月目
            "KKK_CHKS_URG_11_TSKM", // 計画＿直送売上＿１１月目
            "KKK_ZAIKO_RIEKI_11_TSKM", // 計画＿在庫利益＿１１月目
            "KKK_CHKS_RIEKI_11_TSKM", // 計画＿直送利益＿１１月目
            "KKK_ZAIKO_HMPNT_11_TSKM", // 計画＿在庫返品等＿１１月目
            "KKK_CHKS_HMPNT_11_TSKM", // 計画＿直送返品等＿１１月目
            "KKK_ZAIKO_SHHR_RBT_11_TSKM", // 計画＿在庫支払リベート＿１１月目
            "KKK_CHKS_SHHR_RBT_11_TSKM", // 計画＿直送支払リベート＿１１月目
            "KKK_ZAIKO_CNTR_FEE_11_TSKM", // 計画＿在庫センターフィ＿１１月目
            "KKK_CHKS_CNTR_FEE_11_TSKM", // 計画＿直送センターフィ＿１１月目
            "KKK_ZAIKO_URG_12_TSKM", // 計画＿在庫売上＿１２月目
            "KKK_CHKS_URG_12_TSKM", // 計画＿直送売上＿１２月目
            "KKK_ZAIKO_RIEKI_12_TSKM", // 計画＿在庫利益＿１２月目
            "KKK_CHKS_RIEKI_12_TSKM", // 計画＿直送利益＿１２月目
            "KKK_ZAIKO_HMPNT_12_TSKM", // 計画＿在庫返品等＿１２月目
            "KKK_CHKS_HMPNT_12_TSKM", // 計画＿直送返品等＿１２月目
            "KKK_ZAIKO_SHHR_RBT_12_TSKM", // 計画＿在庫支払リベート＿１２月目
            "KKK_CHKS_SHHR_RBT_12_TSKM", // 計画＿直送支払リベート＿１２月目
            "KKK_ZAIKO_CNTR_FEE_12_TSKM", // 計画＿在庫センターフィ＿１２月目
            "KKK_CHKS_CNTR_FEE_12_TSKM", // 計画＿直送センターフィ＿１２月目
            "JSSK_MTSH_ZAIKO_URG_10_TSKM", // 実績見通し＿在庫売上＿１０月目
            "JSSK_MTSH_CHKS_URG_10_TSKM", // 実績見通し＿直送売上＿１０月目
            "JSSK_MTSH_ZAIKO_RIEKI_10_TSKM", // 実績見通し＿在庫利益＿１０月目
            "JSSK_MTSH_CHKS_RIEKI_10_TSKM", // 実績見通し＿直送利益＿１０月目
            "JSSK_MTSH_ZAIKO_HMPNT_10_TSKM", // 実績見通し＿在庫返品等＿１０月目
            "JSSK_MTSH_CHKS_HMPNT_10_TSKM", // 実績見通し＿直送返品等＿１０月目
            "JSSK_MTSH_ZAIKO_SHHR_RBT_10_TSKM", // 実績見通し＿在庫支払リベート＿１０月目
            "JSSK_MTSH_CHKS_SHHR_RBT_10_TSKM", // 実績見通し＿直送支払リベート＿１０月目
            "JSSK_MTSH_ZAIKO_CNTR_FEE_10_TSKM", // 実績見通し＿在庫センターフィ＿１０月目
            "JSSK_MTSH_CHKS_CNTR_FEE_10_TSKM", // 実績見通し＿直送センターフィ＿１０月目
            "JSSK_MTSH_ZAIKO_URG_11_TSKM", // 実績見通し＿在庫売上＿１１月目
            "JSSK_MTSH_CHKS_URG_11_TSKM", // 実績見通し＿直送売上＿１１月目
            "JSSK_MTSH_ZAIKO_RIEKI_11_TSKM", // 実績見通し＿在庫利益＿１１月目
            "JSSK_MTSH_CHKS_RIEKI_11_TSKM", // 実績見通し＿直送利益＿１１月目
            "JSSK_MTSH_ZAIKO_HMPNT_11_TSKM", // 実績見通し＿在庫返品等＿１１月目
            "JSSK_MTSH_CHKS_HMPNT_11_TSKM", // 実績見通し＿直送返品等＿１１月目
            "JSSK_MTSH_ZAIKO_SHHR_RBT_11_TSKM", // 実績見通し＿在庫支払リベート＿１１月目
            "JSSK_MTSH_CHKS_SHHR_RBT_11_TSKM", // 実績見通し＿直送支払リベート＿１１月目
            "JSSK_MTSH_ZAIKO_CNTR_FEE_11_TSKM", // 実績見通し＿在庫センターフィ＿１１月目
            "JSSK_MTSH_CHKS_CNTR_FEE_11_TSKM", // 実績見通し＿直送センターフィ＿１１月目
            "JSSK_MTSH_ZAIKO_URG_12_TSKM", // 実績見通し＿在庫売上＿１２月目
            "JSSK_MTSH_CHKS_URG_12_TSKM", // 実績見通し＿直送売上＿１２月目
            "JSSK_MTSH_ZAIKO_RIEKI_12_TSKM", // 実績見通し＿在庫利益＿１２月目
            "JSSK_MTSH_CHKS_RIEKI_12_TSKM", // 実績見通し＿直送利益＿１２月目
            "JSSK_MTSH_ZAIKO_HMPNT_12_TSKM", // 実績見通し＿在庫返品等＿１２月目
            "JSSK_MTSH_CHKS_HMPNT_12_TSKM", // 実績見通し＿直送返品等＿１２月目
            "JSSK_MTSH_ZAIKO_SHHR_RBT_12_TSKM", // 実績見通し＿在庫支払リベート＿１２月目
            "JSSK_MTSH_CHKS_SHHR_RBT_12_TSKM", // 実績見通し＿直送支払リベート＿１２月目
            "JSSK_MTSH_ZAIKO_CNTR_FEE_12_TSKM", // 実績見通し＿在庫センターフィ＿１２月目
            "JSSK_MTSH_CHKS_CNTR_FEE_12_TSKM", // 実績見通し＿直送センターフィ＿１２月目
            "KKK_URG_KEI" // 計画＿総売上高計
    );
}