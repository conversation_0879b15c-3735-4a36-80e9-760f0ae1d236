package com.ms.bp.shared.common.util;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.application.ExportJobStatusService;
import com.ms.bp.application.ImportJobStatusService;
import com.ms.bp.shared.common.constants.BusinessConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Lambda実行時間後台監視クラス
 * 導入・導出処理専用の後台スレッド監視機能を提供
 * 
 * 注意：このクラスはAWS Lambda環境での使用を前提としており、
 * マルチスレッド環境での複雑性とリスクを含んでいます。
 */
public class BackgroundTimeoutMonitor {
    
    private static final Logger logger = LoggerFactory.getLogger(BackgroundTimeoutMonitor.class);
    
    /**
     * デフォルトのタイムアウト緩衝時間（ミリ秒）
     * 状態更新とクリーンアップ処理に必要な時間を確保
     */
    private static final long DEFAULT_BUFFER_TIME_MS = 60000; // 60秒
    
    /**
     * 監視間隔（ミリ秒）
     * 30秒ごとに残り時間をチェック
     */
    private static final long MONITORING_INTERVAL_MS = 30000; // 30秒
    
    /**
     * Lambda実行コンテキスト
     */
    private final Context lambdaContext;
    
    /**
     * ジョブID（履歴番号）
     */
    private final String jobId;
    
    /**
     * 操作タイプ（導入/導出）
     */
    private final String operationType;
    
    /**
     * タイムアウト緩衝時間（ミリ秒）
     */
    private final long bufferTimeMs;
    
    /**
     * 監視スレッド停止フラグ
     * volatile修飾子により、スレッド間での可視性を保証
     */
    private volatile boolean shouldStop = false;
    
    /**
     * タイムアウト検出フラグ
     * AtomicBooleanによりスレッドセーフな操作を保証
     */
    private final AtomicBoolean timeoutDetected = new AtomicBoolean(false);
    
    /**
     * 現在の処理フェーズ
     * 監視ログ出力用の情報保持
     */
    private final AtomicReference<String> currentPhase = new AtomicReference<>("初期化");
    
    /**
     * 監視スレッド参照
     * 適切なクリーンアップのために保持
     */
    private Thread monitoringThread;
    
    /**
     * 監視開始時刻（ミリ秒）
     */
    private final long startTimeMs;
    
    /**
     * エクスポートジョブステータスサービス
     */
    private ExportJobStatusService exportJobStatusService;
    
    /**
     * インポートジョブステータスサービス
     */
    private ImportJobStatusService importJobStatusService;
    
    /**
     * コンストラクタ
     * 
     * @param lambdaContext Lambda実行コンテキスト
     * @param jobId ジョブID（履歴番号）
     * @param operationType 操作タイプ（BusinessConstants.OPERATION_UPLOAD_CODE または OPERATION_DOWNLOAD_CODE）
     */
    public BackgroundTimeoutMonitor(Context lambdaContext, String jobId, String operationType) {
        this(lambdaContext, jobId, operationType, DEFAULT_BUFFER_TIME_MS);
    }
    
    /**
     * カスタム緩衝時間を指定するコンストラクタ
     * 
     * @param lambdaContext Lambda実行コンテキスト
     * @param jobId ジョブID（履歴番号）
     * @param operationType 操作タイプ
     * @param bufferTimeMs タイムアウト緩衝時間（ミリ秒）
     */
    public BackgroundTimeoutMonitor(Context lambdaContext, String jobId, String operationType, long bufferTimeMs) {
        if (lambdaContext == null) {
            throw new IllegalArgumentException("Lambda contextは必須です");
        }
        if (jobId == null || jobId.trim().isEmpty()) {
            throw new IllegalArgumentException("ジョブIDは必須です");
        }
        if (operationType == null || operationType.trim().isEmpty()) {
            throw new IllegalArgumentException("操作タイプは必須です");
        }
        if (bufferTimeMs < 0) {
            throw new IllegalArgumentException("緩衝時間は0以上である必要があります");
        }
        
        this.lambdaContext = lambdaContext;
        this.jobId = jobId;
        this.operationType = operationType;
        this.bufferTimeMs = bufferTimeMs;
        this.startTimeMs = System.currentTimeMillis();
        
        // サービス初期化（依存性注入の代替）
        this.exportJobStatusService = new ExportJobStatusService();
        this.importJobStatusService = new ImportJobStatusService();
        
        logger.debug("BackgroundTimeoutMonitor初期化: jobId={}, operationType={}, bufferTime={}ms", 
                    jobId, operationType, bufferTimeMs);
    }
    
    /**
     * 後台監視を開始
     * 独立したデーモンスレッドで定期的なタイムアウトチェックを実行
     */
    public void startMonitoring() {
        if (monitoringThread != null && monitoringThread.isAlive()) {
            logger.warn("監視スレッドは既に実行中です: jobId={}", jobId);
            return;
        }
        
        monitoringThread = new Thread(this::monitoringLoop, "TimeoutMonitor-" + jobId);
        monitoringThread.setDaemon(true); // デーモンスレッドとして設定
        monitoringThread.start();
        
        logger.info("後台タイムアウト監視開始: jobId={}, operationType={}", jobId, operationType);
    }
    
    /**
     * 監視ループの実装
     * 定期的に残り時間をチェックし、タイムアウト接近時に適切な処理を実行
     */
    private void monitoringLoop() {
        try {
            while (!shouldStop && !timeoutDetected.get()) {
                long remainingTimeMs = lambdaContext.getRemainingTimeInMillis();
                
                logger.debug("タイムアウト監視チェック: jobId={}, remainingTime={}ms, currentPhase={}", 
                           jobId, remainingTimeMs, currentPhase.get());
                
                // タイムアウト接近判定
                if (remainingTimeMs <= bufferTimeMs) {
                    handleTimeoutApproaching(remainingTimeMs);
                    break;
                }
                
                // 警告レベルの判定とログ出力
                logWarningIfNeeded(remainingTimeMs);
                
                // 監視間隔待機
                Thread.sleep(MONITORING_INTERVAL_MS);
            }
        } catch (InterruptedException e) {
            logger.info("監視スレッドが中断されました: jobId={}", jobId);
            Thread.currentThread().interrupt(); // 中断状態を復元
        } catch (Exception e) {
            logger.error("監視スレッドでエラーが発生しました: jobId={}", jobId, e);
        } finally {
            logger.debug("監視スレッド終了: jobId={}", jobId);
        }
    }
    
    /**
     * タイムアウト接近時の処理
     * 履歴表の状態更新とログ出力を実行
     * 
     * @param remainingTimeMs 残り実行時間（ミリ秒）
     */
    private void handleTimeoutApproaching(long remainingTimeMs) {
        timeoutDetected.set(true);
        long elapsedTimeMs = System.currentTimeMillis() - startTimeMs;
        
        logger.warn("Lambda実行時間タイムアウト接近を検出: jobId={}, operationType={}, " +
                   "remainingTime={}ms, elapsedTime={}ms, currentPhase={}", 
                   jobId, operationType, remainingTimeMs, elapsedTimeMs, currentPhase.get());
        
        // 履歴表状態をシステムエラーに更新
        updateJobStatusToSystemError();
        
        logger.info("タイムアウト接近により処理を中断しました: jobId={}", jobId);
    }
    
    /**
     * 履歴表状態をシステムエラーに更新
     * 操作タイプに応じて適切なサービスを使用
     */
    private void updateJobStatusToSystemError() {
        try {
            if (BusinessConstants.OPERATION_DOWNLOAD_CODE.equals(operationType)) {
                // エクスポート処理の場合
                exportJobStatusService.updateJob(Long.parseLong(jobId), 
                    BusinessConstants.BATCH_STATUS_SYSTEM_ERROR_CODE, null);
                logger.info("エクスポートジョブをシステムエラー状態に更新: jobId={}", jobId);
            } else if (BusinessConstants.OPERATION_UPLOAD_CODE.equals(operationType)) {
                // インポート処理の場合
                importJobStatusService.updateJob(Long.parseLong(jobId), 
                    BusinessConstants.BATCH_STATUS_SYSTEM_ERROR_CODE, null);
                logger.info("インポートジョブをシステムエラー状態に更新: jobId={}", jobId);
            } else {
                logger.warn("未知の操作タイプです: operationType={}, jobId={}", operationType, jobId);
            }
        } catch (Exception e) {
            logger.error("履歴表状態更新エラー: jobId={}, operationType={}", jobId, operationType, e);
        }
    }
    
    /**
     * 警告レベルに応じたログ出力
     * 
     * @param remainingTimeMs 残り実行時間（ミリ秒）
     */
    private void logWarningIfNeeded(long remainingTimeMs) {
        if (remainingTimeMs <= bufferTimeMs * 2) { // 緩衝時間の2倍以下で警告
            logger.warn("Lambda実行時間が警告レベルに達しました: jobId={}, remainingTime={}ms, currentPhase={}", 
                       jobId, remainingTimeMs, currentPhase.get());
        }
    }
    
    /**
     * 監視を停止
     * 監視スレッドを安全に終了させる
     */
    public void stopMonitoring() {
        shouldStop = true;
        
        if (monitoringThread != null && monitoringThread.isAlive()) {
            try {
                // スレッドの終了を待機（最大5秒）
                monitoringThread.interrupt();
                monitoringThread.join(5000);
                
                if (monitoringThread.isAlive()) {
                    logger.warn("監視スレッドの終了タイムアウト: jobId={}", jobId);
                } else {
                    logger.debug("監視スレッドが正常に終了しました: jobId={}", jobId);
                }
            } catch (InterruptedException e) {
                logger.warn("監視スレッド終了待機が中断されました: jobId={}", jobId);
                Thread.currentThread().interrupt();
            }
        }
    }
    
    /**
     * 現在の処理フェーズを更新
     * 監視ログの詳細情報として使用
     * 
     * @param phase 処理フェーズ名
     */
    public void updateCurrentPhase(String phase) {
        currentPhase.set(phase);
        logger.debug("処理フェーズ更新: jobId={}, phase={}", jobId, phase);
    }
    
    /**
     * タイムアウトが検出されたかを確認
     * 
     * @return タイムアウトが検出された場合はtrue
     */
    public boolean isTimeoutDetected() {
        return timeoutDetected.get();
    }
    
    /**
     * 監視統計情報をログ出力
     * 処理完了時の診断情報として使用
     */
    public void logMonitoringStats() {
        long elapsedTimeMs = System.currentTimeMillis() - startTimeMs;
        long remainingTimeMs = lambdaContext.getRemainingTimeInMillis();
        double timeUsagePercent = (double) elapsedTimeMs / (elapsedTimeMs + remainingTimeMs) * 100;
        
        logger.info("後台監視統計: jobId={}, operationType={}, elapsedTime={}ms, " +
                   "remainingTime={}ms, timeUsage={:.1f}%, timeoutDetected={}", 
                   jobId, operationType, elapsedTimeMs, remainingTimeMs, 
                   timeUsagePercent, timeoutDetected.get());
    }
    
    /**
     * リソースクリーンアップ
     * 監視スレッドの停止と関連リソースの解放
     */
    public void cleanup() {
        logger.debug("BackgroundTimeoutMonitor クリーンアップ開始: jobId={}", jobId);
        
        stopMonitoring();
        
        // 統計情報をログ出力
        logMonitoringStats();
        
        logger.debug("BackgroundTimeoutMonitor クリーンアップ完了: jobId={}", jobId);
    }
}
