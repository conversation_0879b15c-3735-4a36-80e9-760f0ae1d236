package com.ms.bp.shared.common.io.buffer;

import com.opencsv.CSVWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * CSV書き込みバッファ管理クラス
 * バッチ書き込みによるI/O効率向上を実現
 */
public class CsvWriteBuffer {
    private static final Logger logger = LoggerFactory.getLogger(CsvWriteBuffer.class);
    
    private final CSVWriter csvWriter;
    private final int bufferSize;
    private final List<String[]> recordBuffer;
    private final boolean enableBatchWrite;
    
    /**
     * コンストラクタ
     * 
     * @param csvWriter CSV書き込み用ライター
     * @param bufferSize バッファサイズ（レコード数）
     * @param enableBatchWrite バッチ書き込み有効フラグ
     */
    public CsvWriteBuffer(CSVWriter csvWriter, int bufferSize, boolean enableBatchWrite) {
        this.csvWriter = csvWriter;
        this.bufferSize = Math.max(1, bufferSize); // 最小値1を保証
        this.enableBatchWrite = enableBatchWrite;
        this.recordBuffer = enableBatchWrite ? new ArrayList<>(this.bufferSize) : null;
        
        logger.debug("CsvWriteBuffer初期化: bufferSize={}, enableBatchWrite={}", 
                    this.bufferSize, enableBatchWrite);
    }
    
    /**
     * レコードをバッファに追加
     * バッファが満杯になった場合は自動的にフラッシュ
     * 
     * @param record 書き込むレコード
     * @throws IOException 書き込みエラー
     */
    public void addRecord(String[] record) throws IOException {
        if (!enableBatchWrite) {
            // バッチ書き込み無効の場合は即座に書き込み
            csvWriter.writeNext(record);
            return;
        }
        
        recordBuffer.add(record);
        
        // バッファが満杯になった場合はフラッシュ
        if (recordBuffer.size() >= bufferSize) {
            flush();
        }
    }
    
    /**
     * バッファ内の全レコードを書き込み
     * CSVWriter.writeAllメソッドを使用してバッチ書き込みを実行
     *
     * @throws IOException 書き込みエラー
     */
    public void flush() throws IOException {
        if (!enableBatchWrite || recordBuffer.isEmpty()) {
            return;
        }

        logger.debug("CSVバッファフラッシュ開始: レコード数={}", recordBuffer.size());

        try {
            // CSVWriter.writeAllを使用してバッチ書き込み実行
            csvWriter.writeAll(recordBuffer);

            // CSVWriterのフラッシュ
            csvWriter.flush();

            logger.debug("CSVバッファフラッシュ完了: レコード数={}", recordBuffer.size());

        } finally {
            // バッファをクリア
            recordBuffer.clear();
        }
    }
    
    /**
     * 残りのレコードを全て書き込んでリソースをクリーンアップ
     * 
     * @throws IOException 書き込みエラー
     */
    public void close() throws IOException {
        try {
            // 残りのレコードをフラッシュ
            flush();
        } finally {
            // CSVWriterのクローズ
            if (csvWriter != null) {
                csvWriter.close();
            }
        }
    }
    

    /**
     * バッファが空かどうかを確認
     * 
     * @return バッファが空の場合true
     */
    public boolean isEmpty() {
        return !enableBatchWrite || recordBuffer.isEmpty();
    }
}
