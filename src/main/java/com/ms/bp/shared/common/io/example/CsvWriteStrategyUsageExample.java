package com.ms.bp.shared.common.io.example;

import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.io.model.FileFormat;
import com.ms.bp.shared.common.io.options.ExportOptions;
import com.ms.bp.shared.common.io.strategy.impl.CsvWriteStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

/**
 * CsvWriteStrategy使用例
 * パフォーマンス最適化が自動適用される新しい実装の使用方法を示す
 */
public class CsvWriteStrategyUsageExample {
    private static final Logger logger = LoggerFactory.getLogger(CsvWriteStrategyUsageExample.class);
    
    /**
     * 基本的な使用例
     * パフォーマンス最適化は自動的に適用される
     */
    public static void basicUsageExample() throws IOException {
        logger.info("=== CsvWriteStrategy基本使用例 ===");
        
        // 1. 列定義
        List<String> columns = Arrays.asList("id", "name", "email", "department");
        
        // 2. ExportOptions作成（パフォーマンス設定は不要）
        ExportOptions options = ExportOptions.builder()
                .format(FileFormat.CSV)
                .includeHeader(true)
                .columns(columns)
                .batchSize(BusinessConstants.CSV_DOWNLOAD_BATCH_SIZE)
                .build();
        
        // 3. CsvWriteStrategy作成
        CsvWriteStrategy<Map<String, Object>> strategy = new CsvWriteStrategy<>();
        
        // 4. 出力ストリーム準備
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        try {
            // 5. ヘッダー書き込み
            strategy.writeHeader(columns, outputStream, options);
            
            // 6. データ書き込み（パフォーマンス最適化が自動適用）
            List<Map<String, Object>> testData = generateSampleData(1000);
            for (Map<String, Object> record : testData) {
                strategy.writeRecord(record, columns, outputStream, options);
            }
            
            // 7. 完了処理
            strategy.finish(outputStream, options);
            
            logger.info("CSV書き込み完了: サイズ={}KB, レコード数={}", 
                       outputStream.size() / 1024, testData.size());
            
        } finally {
            outputStream.close();
        }
    }
    
    /**
     * フィールドマッピング使用例
     * パフォーマンス最適化とフィールドマッピングキャッシュが自動適用される
     */
    public static void fieldMappingExample() throws IOException {
        logger.info("=== フィールドマッピング使用例 ===");
        
        // 1. 列定義
        List<String> columns = Arrays.asList("id", "name", "email", "department");
        
        // 2. フィールドマッピング定義
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("id", "ID");
        fieldMapping.put("name", "氏名");
        fieldMapping.put("email", "メールアドレス");
        fieldMapping.put("department", "部署");
        
        // 3. ExportOptions作成（フィールドマッピング有効）
        ExportOptions options = ExportOptions.builder()
                .format(FileFormat.CSV)
                .includeHeader(true)
                .columns(columns)
                .batchSize(BusinessConstants.CSV_DOWNLOAD_BATCH_SIZE)
                .enableFieldMapping(true)
                .fieldHeaderMapping(fieldMapping)
                .build();
        
        // 4. CsvWriteStrategy作成
        CsvWriteStrategy<Map<String, Object>> strategy = new CsvWriteStrategy<>();
        
        // 5. 出力ストリーム準備
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        try {
            // 6. ヘッダー書き込み（日本語ヘッダーに変換）
            strategy.writeHeader(columns, outputStream, options);
            
            // 7. データ書き込み（フィールドマッピングキャッシュが自動適用）
            List<Map<String, Object>> testData = generateSampleData(500);
            for (Map<String, Object> record : testData) {
                strategy.writeRecord(record, columns, outputStream, options);
            }
            
            // 8. 完了処理
            strategy.finish(outputStream, options);
            
            logger.info("フィールドマッピング付きCSV書き込み完了: サイズ={}KB, レコード数={}", 
                       outputStream.size() / 1024, testData.size());
            
        } finally {
            outputStream.close();
        }
    }
    
    /**
     * 大容量データ処理例
     * バッチ書き込みとオブジェクトプールの効果を確認
     */
    public static void largeDataExample() throws IOException {
        logger.info("=== 大容量データ処理例 ===");
        
        // 1. 列定義
        List<String> columns = Arrays.asList("id", "name", "email", "department", "salary", "joinDate");
        
        // 2. ExportOptions作成
        ExportOptions options = ExportOptions.builder()
                .format(FileFormat.CSV)
                .includeHeader(true)
                .columns(columns)
                .batchSize(BusinessConstants.CSV_DOWNLOAD_BATCH_SIZE)
                .build();
        
        // 3. CsvWriteStrategy作成
        CsvWriteStrategy<Map<String, Object>> strategy = new CsvWriteStrategy<>();
        
        // 4. 出力ストリーム準備
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 5. ヘッダー書き込み
            strategy.writeHeader(columns, outputStream, options);
            
            // 6. 大容量データ書き込み（10,000件）
            List<Map<String, Object>> testData = generateSampleData(10000);
            for (Map<String, Object> record : testData) {
                strategy.writeRecord(record, columns, outputStream, options);
            }
            
            // 7. 完了処理
            strategy.finish(outputStream, options);
            
            long endTime = System.currentTimeMillis();
            
            logger.info("大容量データCSV書き込み完了: サイズ={}KB, レコード数={}, 処理時間={}ms", 
                       outputStream.size() / 1024, testData.size(), endTime - startTime);
            
        } finally {
            outputStream.close();
        }
    }
    
    /**
     * サンプルデータ生成
     * 
     * @param count 生成件数
     * @return サンプルデータリスト
     */
    private static List<Map<String, Object>> generateSampleData(int count) {
        List<Map<String, Object>> data = new ArrayList<>(count);
        Random random = new Random(12345); // 固定シードで再現性確保
        
        for (int i = 0; i < count; i++) {
            Map<String, Object> record = new HashMap<>();
            record.put("id", i + 1);
            record.put("name", "テストユーザー" + (i + 1));
            record.put("email", "user" + (i + 1) + "@example.com");
            record.put("department", "部署" + (random.nextInt(10) + 1));
            record.put("salary", 300000 + random.nextInt(500000));
            record.put("joinDate", "2024-" + String.format("%02d", random.nextInt(12) + 1) + "-01");
            data.add(record);
        }
        
        return data;
    }
    
    /**
     * 使用例実行
     */
    public static void main(String[] args) {
        try {
            basicUsageExample();
            fieldMappingExample();
            largeDataExample();
            
            logger.info("=== 全ての使用例が正常に完了しました ===");
            logger.info("パフォーマンス最適化設定:");
            logger.info("- CSV書き込みバッファ: {}レコード", BusinessConstants.CSV_WRITE_BUFFER_SIZE);
            logger.info("- 出力ストリームバッファ: {}KB", BusinessConstants.OUTPUT_STREAM_BUFFER_SIZE / 1024);
            logger.info("- オブジェクトプール最大サイズ: {}個", BusinessConstants.STRING_ARRAY_POOL_MAX_SIZE);
            
        } catch (IOException e) {
            logger.error("使用例実行中にエラーが発生しました", e);
        }
    }
}
