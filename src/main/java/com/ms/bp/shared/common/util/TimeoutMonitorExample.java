package com.ms.bp.shared.common.util;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.shared.common.exception.TimeoutApproachingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * TimeoutMonitor使用例
 * Lambda関数での実行時間監視の実装パターンを示すサンプルクラス
 * 
 * 注意：このクラスは実装例として提供されており、実際のプロダクションコードでは
 * TaskOrchestrationServiceで既に統合されています。
 */
public class TimeoutMonitorExample {
    
    private static final Logger logger = LoggerFactory.getLogger(TimeoutMonitorExample.class);
    
    /**
     * 基本的なTimeoutMonitor使用例
     * 
     * @param context Lambda実行コンテキスト
     * @param jobId ジョブID
     */
    public void basicUsageExample(Context context, String jobId) {
        // TimeoutMonitorを初期化（デフォルト60秒緩衝時間）
        TimeoutMonitor timeoutMonitor = new TimeoutMonitor(context, jobId);
        
        try {
            // 処理1: データ取得
            timeoutMonitor.checkTimeout("データ取得開始前");
            performDataRetrieval();
            
            // 処理2: データ変換
            timeoutMonitor.checkTimeout("データ変換開始前");
            performDataTransformation();
            
            // 処理3: ファイル生成
            timeoutMonitor.checkTimeout("ファイル生成開始前");
            performFileGeneration();
            
            // 処理4: S3アップロード
            timeoutMonitor.checkTimeout("S3アップロード開始前");
            performS3Upload();
            
            // 処理完了時の統計情報をログ出力
            timeoutMonitor.logExecutionStats();
            
        } catch (TimeoutApproachingException e) {
            logger.warn("Lambda実行時間タイムアウト接近により処理を中断: {}", e.getLogInfo());
            // 適切なクリーンアップ処理を実行
            performCleanup();
            throw e; // 上位層で適切に処理される
        }
    }
    
    /**
     * カスタム緩衝時間を使用する例
     * 
     * @param context Lambda実行コンテキスト
     * @param jobId ジョブID
     */
    public void customBufferTimeExample(Context context, String jobId) {
        // カスタム緩衝時間（90秒）を指定
        long customBufferTimeMs = 90000; // 90秒
        TimeoutMonitor timeoutMonitor = new TimeoutMonitor(context, jobId, customBufferTimeMs);
        
        try {
            // 長時間処理の場合は緩衝時間を長めに設定
            timeoutMonitor.checkTimeout("長時間処理開始前");
            performLongRunningProcess();
            
        } catch (TimeoutApproachingException e) {
            logger.warn("カスタム緩衝時間での タイムアウト接近: bufferTime={}ms, details={}", 
                       customBufferTimeMs, e.getLogInfo());
            throw e;
        }
    }
    
    /**
     * 安全実行チェックを使用する例
     * 
     * @param context Lambda実行コンテキスト
     * @param jobId ジョブID
     */
    public void safeExecutionCheckExample(Context context, String jobId) {
        TimeoutMonitor timeoutMonitor = new TimeoutMonitor(context, jobId);
        
        // 推定処理時間（5分）
        long estimatedProcessingTimeMs = 300000; // 5分
        
        if (timeoutMonitor.canSafelyExecute(estimatedProcessingTimeMs)) {
            logger.info("推定処理時間{}msでの安全実行が可能です", estimatedProcessingTimeMs);
            performEstimatedProcess();
        } else {
            logger.warn("推定処理時間{}msでの安全実行ができません。残り時間={}ms", 
                       estimatedProcessingTimeMs, timeoutMonitor.getRemainingTimeMs());
            // 代替処理または処理スキップ
            performAlternativeProcess();
        }
    }
    
    /**
     * 警告レベル監視の例
     * 
     * @param context Lambda実行コンテキスト
     * @param jobId ジョブID
     */
    public void warningLevelMonitoringExample(Context context, String jobId) {
        TimeoutMonitor timeoutMonitor = new TimeoutMonitor(context, jobId);
        
        // 定期的な警告レベルチェック
        String warningLevel = timeoutMonitor.getWarningLevel();
        
        switch (warningLevel) {
            case "INFO":
                logger.info("実行時間は正常範囲内です: remainingTime={}ms", 
                           timeoutMonitor.getRemainingTimeMs());
                break;
            case "WARN":
                logger.warn("実行時間が警告レベルに達しました: remainingTime={}ms", 
                           timeoutMonitor.getRemainingTimeMs());
                // 処理の優先度を調整
                adjustProcessingPriority();
                break;
            case "ERROR":
                logger.error("実行時間が危険レベルに達しました: remainingTime={}ms", 
                            timeoutMonitor.getRemainingTimeMs());
                // 緊急処理モードに切り替え
                switchToEmergencyMode();
                break;
        }
    }
    
    /**
     * 複数チェックポイントでの監視例
     * 
     * @param context Lambda実行コンテキスト
     * @param jobId ジョブID
     */
    public void multipleCheckpointsExample(Context context, String jobId) {
        TimeoutMonitor timeoutMonitor = new TimeoutMonitor(context, jobId);
        
        try {
            // フェーズ1: 初期化
            timeoutMonitor.checkTimeout("初期化フェーズ");
            performInitialization();
            
            // フェーズ2: データ処理
            timeoutMonitor.checkTimeout("データ処理フェーズ");
            performDataProcessing();
            
            // フェーズ3: 結果生成
            timeoutMonitor.checkTimeout("結果生成フェーズ");
            performResultGeneration();
            
            // フェーズ4: 最終化
            timeoutMonitor.checkTimeout("最終化フェーズ");
            performFinalization();
            
        } catch (TimeoutApproachingException e) {
            logger.warn("フェーズ処理中にタイムアウト接近: checkPoint={}, details={}", 
                       e.getCheckPointName(), e.getLogInfo());
            
            // チェックポイント別の適切な処理
            handleTimeoutByCheckpoint(e.getCheckPointName());
            throw e;
        }
    }
    
    // ==================== プライベートメソッド（サンプル処理） ====================
    
    private void performDataRetrieval() {
        logger.debug("データ取得処理を実行中...");
        // 実際のデータ取得処理をシミュレート
        simulateProcessing(1000);
    }
    
    private void performDataTransformation() {
        logger.debug("データ変換処理を実行中...");
        simulateProcessing(2000);
    }
    
    private void performFileGeneration() {
        logger.debug("ファイル生成処理を実行中...");
        simulateProcessing(3000);
    }
    
    private void performS3Upload() {
        logger.debug("S3アップロード処理を実行中...");
        simulateProcessing(2000);
    }
    
    private void performLongRunningProcess() {
        logger.debug("長時間処理を実行中...");
        simulateProcessing(5000);
    }
    
    private void performEstimatedProcess() {
        logger.debug("推定処理を実行中...");
        simulateProcessing(3000);
    }
    
    private void performAlternativeProcess() {
        logger.debug("代替処理を実行中...");
        simulateProcessing(1000);
    }
    
    private void performInitialization() {
        logger.debug("初期化処理を実行中...");
        simulateProcessing(500);
    }
    
    private void performDataProcessing() {
        logger.debug("データ処理を実行中...");
        simulateProcessing(2000);
    }
    
    private void performResultGeneration() {
        logger.debug("結果生成処理を実行中...");
        simulateProcessing(1500);
    }
    
    private void performFinalization() {
        logger.debug("最終化処理を実行中...");
        simulateProcessing(1000);
    }
    
    private void performCleanup() {
        logger.info("クリーンアップ処理を実行中...");
        simulateProcessing(500);
    }
    
    private void adjustProcessingPriority() {
        logger.info("処理優先度を調整中...");
    }
    
    private void switchToEmergencyMode() {
        logger.info("緊急処理モードに切り替え中...");
    }
    
    private void handleTimeoutByCheckpoint(String checkPointName) {
        logger.info("チェックポイント別タイムアウト処理: {}", checkPointName);
    }
    
    /**
     * 処理時間をシミュレートするヘルパーメソッド
     * 
     * @param durationMs 処理時間（ミリ秒）
     */
    private void simulateProcessing(long durationMs) {
        try {
            Thread.sleep(durationMs);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("処理がインタラプトされました", e);
        }
    }
}
