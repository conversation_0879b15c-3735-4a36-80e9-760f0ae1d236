package com.ms.bp.shared.common.exception;

import lombok.Getter;

/**
 * Lambda実行時間タイムアウト接近例外
 * AWS Lambda関数の実行時間がタイムアウトに接近した際に投げられる例外
 * 
 * この例外は予防的な措置として使用され、Lambda関数が強制終了される前に
 * 適切なクリーンアップ処理や状態更新を実行するために設計されています。
 */
@Getter
public class TimeoutApproachingException extends RuntimeException {
    
    /**
     * ジョブID
     */
    private final String jobId;
    
    /**
     * タイムアウト検出時のチェックポイント名
     */
    private final String checkPointName;
    
    /**
     * 残り実行時間（ミリ秒）
     */
    private final long remainingTimeMs;
    
    /**
     * 経過時間（ミリ秒）
     */
    private final long elapsedTimeMs;
    
    /**
     * 例外発生時刻（ミリ秒）
     */
    private final long exceptionTimeMs;
    
    /**
     * コンストラクタ
     * 
     * @param jobId ジョブID
     * @param checkPointName チェックポイント名
     * @param remainingTimeMs 残り実行時間（ミリ秒）
     * @param elapsedTimeMs 経過時間（ミリ秒）
     */
    public TimeoutApproachingException(String jobId, String checkPointName, 
                                     long remainingTimeMs, long elapsedTimeMs) {
        super(createMessage(jobId, checkPointName, remainingTimeMs, elapsedTimeMs));
        
        this.jobId = jobId;
        this.checkPointName = checkPointName;
        this.remainingTimeMs = remainingTimeMs;
        this.elapsedTimeMs = elapsedTimeMs;
        this.exceptionTimeMs = System.currentTimeMillis();
    }
    
    /**
     * 原因となる例外を含むコンストラクタ
     * 
     * @param jobId ジョブID
     * @param checkPointName チェックポイント名
     * @param remainingTimeMs 残り実行時間（ミリ秒）
     * @param elapsedTimeMs 経過時間（ミリ秒）
     * @param cause 原因となる例外
     */
    public TimeoutApproachingException(String jobId, String checkPointName, 
                                     long remainingTimeMs, long elapsedTimeMs, Throwable cause) {
        super(createMessage(jobId, checkPointName, remainingTimeMs, elapsedTimeMs), cause);
        
        this.jobId = jobId;
        this.checkPointName = checkPointName;
        this.remainingTimeMs = remainingTimeMs;
        this.elapsedTimeMs = elapsedTimeMs;
        this.exceptionTimeMs = System.currentTimeMillis();
    }
    
    /**
     * 例外メッセージを生成
     * 
     * @param jobId ジョブID
     * @param checkPointName チェックポイント名
     * @param remainingTimeMs 残り実行時間（ミリ秒）
     * @param elapsedTimeMs 経過時間（ミリ秒）
     * @return 例外メッセージ
     */
    private static String createMessage(String jobId, String checkPointName, 
                                      long remainingTimeMs, long elapsedTimeMs) {
        return String.format(
            "Lambda実行時間がタイムアウトに接近しました: " +
            "jobId=%s, checkPoint=%s, remainingTime=%dms, elapsedTime=%dms",
            jobId, checkPointName, remainingTimeMs, elapsedTimeMs
        );
    }
    
    /**
     * 残り実行時間を秒単位で取得
     * 
     * @return 残り実行時間（秒）
     */
    public long getRemainingTimeSeconds() {
        return remainingTimeMs / 1000;
    }
    
    /**
     * 経過時間を秒単位で取得
     * 
     * @return 経過時間（秒）
     */
    public long getElapsedTimeSeconds() {
        return elapsedTimeMs / 1000;
    }
    
    /**
     * 実行時間の使用率を取得（パーセント）
     * 
     * @return 実行時間使用率（0.0-100.0）
     */
    public double getTimeUsagePercentage() {
        long totalTime = remainingTimeMs + elapsedTimeMs;
        if (totalTime == 0) {
            return 0.0;
        }
        return (double) elapsedTimeMs / totalTime * 100.0;
    }
    
    /**
     * タイムアウトまでの推定時間を取得（ミリ秒）
     * 
     * @return タイムアウトまでの推定時間（ミリ秒）
     */
    public long getEstimatedTimeToTimeout() {
        return remainingTimeMs;
    }
    
    /**
     * 詳細な診断情報を含む文字列表現を取得
     * 
     * @return 詳細情報文字列
     */
    public String getDetailedInfo() {
        return String.format(
            "TimeoutApproachingException Details:\n" +
            "  Job ID: %s\n" +
            "  Check Point: %s\n" +
            "  Remaining Time: %d ms (%d seconds)\n" +
            "  Elapsed Time: %d ms (%d seconds)\n" +
            "  Time Usage: %.1f%%\n" +
            "  Exception Time: %d ms\n" +
            "  Message: %s",
            jobId, checkPointName, 
            remainingTimeMs, getRemainingTimeSeconds(),
            elapsedTimeMs, getElapsedTimeSeconds(),
            getTimeUsagePercentage(),
            exceptionTimeMs,
            getMessage()
        );
    }
    
    /**
     * ログ出力用の簡潔な情報を取得
     * 
     * @return ログ用情報文字列
     */
    public String getLogInfo() {
        return String.format(
            "jobId=%s, checkPoint=%s, remaining=%dms, elapsed=%dms, usage=%.1f%%",
            jobId, checkPointName, remainingTimeMs, elapsedTimeMs, getTimeUsagePercentage()
        );
    }
    
    /**
     * この例外が緊急レベルかどうかを判定
     * 残り時間が非常に少ない場合は緊急レベルとする
     * 
     * @return 緊急レベルの場合はtrue
     */
    public boolean isCriticalLevel() {
        // 残り時間が30秒以下の場合は緊急レベル
        return remainingTimeMs <= 30000;
    }
    
    /**
     * ServiceExceptionとして変換
     * 既存の例外処理システムとの統合用
     * 
     * @return ServiceException
     */
    public ServiceException toServiceException() {
        return new ServiceException(50002, this.getMessage());
    }
}
