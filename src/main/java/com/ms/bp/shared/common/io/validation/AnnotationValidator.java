package com.ms.bp.shared.common.io.validation;

import com.ms.bp.shared.common.exception.ValidationError;


import java.lang.invoke.MethodHandle;
import java.lang.invoke.MethodHandles;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * アノテーションベースのバリデーター
 * パフォーマンス最適化：MethodHandle とバリデーションメタデータのキャッシュ機能を実装
 * GlobalCodeConstants統合対応版 - JDK 21 最適化バージョン
 */
public class AnnotationValidator {

    private static final Logger logger = LoggerFactory.getLogger(AnnotationValidator.class);

    /** MethodHandles.Lookup インスタンス（MethodHandle 作成用） */
    private static final MethodHandles.Lookup LOOKUP = MethodHandles.lookup();

    /** バリデーションメタデータのキャッシュ（スレッドセーフ） */
    private static final Map<Class<?>, ValidationMetadata[]> VALIDATION_METADATA_CACHE = new ConcurrentHashMap<>();

    /** 半角数字検証用の事前コンパイル済みパターン */
    private static final Pattern NUMERIC_HALF_WIDTH_PATTERN = Pattern.compile("^[0-9]+$");

    /** 半角英数字検証用の事前コンパイル済みパターン */
    private static final Pattern HALF_WIDTH_ALPHANUMERIC_PATTERN = Pattern.compile("^[a-zA-Z0-9]+$");

    /** 数値フォーマット検証用の事前コンパイル済みパターン（0.01-999.99形式） */
    private static final Pattern DECIMAL_FORMAT_PATTERN = Pattern.compile("^[0-9]{1,3}\\.[0-9]{2}$");

    /** 数値フォーマット検証用の最小値 */
    private static final java.math.BigDecimal DECIMAL_MIN_VALUE = new java.math.BigDecimal("0.01");

    /** 数値フォーマット検証用の最大値 */
    private static final java.math.BigDecimal DECIMAL_MAX_VALUE = new java.math.BigDecimal("999.99");

    /**
     * バリデーションメタデータクラス
     * フィールド情報、MethodHandle、および検証ルールを保持
     */
    static class ValidationMetadata {
        @Getter
        private final Field field;
        @Getter
        private final String fieldName;
        private final MethodHandle getter;
        @Getter
        private final List<ValidationRule> validationRules;
        private final boolean hasValidationRules;

        public ValidationMetadata(Field field) {
            this.field = field;
            this.fieldName = field.getName();

            try {
                field.setAccessible(true);
                // getter の MethodHandle を作成
                this.getter = LOOKUP.unreflectGetter(field);
            } catch (IllegalAccessException e) {
                throw new RuntimeException("フィールドの MethodHandle 作成エラー: " + field.getName(), e);
            }

            // 外部のValidationMetadataクラスを使用して検証ルールを取得
            com.ms.bp.shared.common.io.validation.ValidationMetadata externalMetadata =
                new com.ms.bp.shared.common.io.validation.ValidationMetadata(field);
            this.validationRules = externalMetadata.getValidationRules();
            this.hasValidationRules = !validationRules.isEmpty();
        }

        /**
         * MethodHandle を使用してフィールド値を取得
         *
         * @param target 対象オブジェクト
         * @return フィールドの値
         * @throws Throwable MethodHandle 呼び出し時の例外
         */
        public Object getValue(Object target) throws Throwable {
            return getter.invoke(target);
        }

        public boolean hasValidationRules() {
            return hasValidationRules;
        }
    }

    /**
     * オブジェクトのアノテーションを使用して検証を実行
     * パフォーマンス最適化：MethodHandle とキャッシュされたメタデータを使用
     *
     * @param obj 検証対象のオブジェクト
     * @return 検証エラーのリスト
     */
    public static List<ValidationError> validate(Object obj) {
        List<ValidationError> errors = new ArrayList<>();

        if (obj == null) {
            return errors;
        }

        Class<?> clazz = obj.getClass();

        // キャッシュされたバリデーションメタデータを取得（初回は自動生成）
        ValidationMetadata[] validationMetadataArray = getValidationMetadata(clazz);

        for (ValidationMetadata metadata : validationMetadataArray) {
            // 検証ルールが存在しない場合はスキップ
            if (!metadata.hasValidationRules()) {
                continue;
            }

            try {
                // MethodHandle を使用して値を取得（反射より高速）
                Object value = metadata.getValue(obj);
                String fieldName = metadata.getFieldName();

                // 各検証ルールを実行
                for (ValidationRule rule : metadata.getValidationRules()) {
                    executeValidationRule(rule, fieldName, value, errors);
                }

            } catch (Throwable e) {
                errors.add(new ValidationError(metadata.getFieldName(), null,
                        "フィールドアクセスエラー: " + e.getMessage()));
            }
        }

        return errors;
    }

    /**
     * 複数オブジェクトのバッチ検証
     *
     * @param objects 検証対象のオブジェクトリスト
     * @param <T> オブジェクトの型
     * @return 各オブジェクトの検証エラーリスト
     */
    public static <T> List<List<ValidationError>> validateBatch(List<T> objects) {
        if (objects == null || objects.isEmpty()) {
            return new ArrayList<>();
        }

        // 最初のオブジェクトでメタデータを事前ロード
        if (objects.getFirst() != null) {
            getValidationMetadata(objects.getFirst().getClass());
        }

        return objects.stream()
                .map(AnnotationValidator::validate)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * バリデーションメタデータを取得（キャッシュ機能付き）
     * 初回アクセス時にメタデータを生成してキャッシュし、以降はキャッシュから取得
     *
     * @param clazz 検証対象のクラス
     * @return バリデーションメタデータの配列
     */
    private static ValidationMetadata[] getValidationMetadata(Class<?> clazz) {
        return VALIDATION_METADATA_CACHE.computeIfAbsent(clazz, k -> {
            logger.debug("バリデーションメタデータを生成中: {}", k.getSimpleName());

            Field[] fields = k.getDeclaredFields();
            ValidationMetadata[] metadataArray = new ValidationMetadata[fields.length];

            for (int i = 0; i < fields.length; i++) {
                metadataArray[i] = new ValidationMetadata(fields[i]);
            }

            logger.debug("バリデーションメタデータ生成完了: {} ({}個のフィールド)",
                    k.getSimpleName(), metadataArray.length);

            return metadataArray;
        });
    }

    /**
     * 検証ルールを実行
     * Switch Expression を使用した最適化版
     *
     * @param rule 検証ルール
     * @param fieldName フィールド名
     * @param value フィールドの値
     * @param errors エラーリスト
     */
    private static void executeValidationRule(ValidationRule rule, String fieldName, Object value, List<ValidationError> errors) {
        // 空文字列チェックの共通処理
        boolean isEmptyString = value instanceof String && ((String) value).trim().isEmpty();

        switch (rule.getType()) {
            case REQUIRED -> validateRequired(fieldName, value, rule, errors);
            case RANGE -> {
                if (value != null && !isEmptyString) {
                    validateRange(fieldName, value, rule, errors);
                }
            }
            case NUMERIC_HALF_WIDTH -> {
                if (value != null && !isEmptyString) {
                    validateNumericHalfWidth(fieldName, value.toString(), rule, errors);
                }
            }
            case HALF_WIDTH_ALPHANUMERIC -> {
                if (value != null && !isEmptyString) {
                    validateHalfWidthAlphanumeric(fieldName, value.toString(), rule, errors);
                }
            }
            case DECIMAL_FORMAT -> {
                if (value != null && !isEmptyString) {
                    validateDecimalFormat(fieldName, value.toString(), rule, errors);
                }
            }
        }
    }



    // 以下、各検証メソッド（最適化版）

    /**
     * 必須チェック検証（統一版）
     */
    private static void validateRequired(String fieldName, Object value, ValidationRule rule, List<ValidationError> errors) {
        if (value == null || (value instanceof String && ((String) value).trim().isEmpty())) {
            String displayName = rule.hasCustomFieldName() ? rule.getCustomFieldName() : fieldName;

            String message = ErrorMessageResolver.resolveMessage(
                rule.getErrorCode(),
                rule.getMessageTemplate(),
                ErrorMessageResolver.ValidationType.REQUIRED,
                displayName
            );

            errors.add(new ValidationError(fieldName, displayName, value, message, rule.getErrorCode()));
        }
    }

    /**
     * 半角数字チェック検証（統一版）
     */
    private static void validateNumericHalfWidth(String fieldName, String value, ValidationRule rule, List<ValidationError> errors) {
        if (!NUMERIC_HALF_WIDTH_PATTERN.matcher(value).matches()) {
            String displayName = rule.hasCustomFieldName() ? rule.getCustomFieldName() : fieldName;

            String message = ErrorMessageResolver.resolveMessage(
                rule.getErrorCode(),
                rule.getMessageTemplate(),
                ErrorMessageResolver.ValidationType.NUMERIC_HALF_WIDTH,
                displayName
            );

            errors.add(new ValidationError(fieldName, displayName, value, message, rule.getErrorCode()));
        }
    }

    /**
     * 半角英数字チェック検証（統一版）
     * 半角英文字（a-z, A-Z）と半角数字（0-9）のみを許可
     */
    private static void validateHalfWidthAlphanumeric(String fieldName, String value, ValidationRule rule, List<ValidationError> errors) {
        if (!HALF_WIDTH_ALPHANUMERIC_PATTERN.matcher(value).matches()) {
            String displayName = rule.hasCustomFieldName() ? rule.getCustomFieldName() : fieldName;

            String message = ErrorMessageResolver.resolveMessage(
                rule.getErrorCode(),
                rule.getMessageTemplate(),
                ErrorMessageResolver.ValidationType.HALF_WIDTH_ALPHANUMERIC,
                displayName
            );

            errors.add(new ValidationError(fieldName, displayName, value, message, rule.getErrorCode()));
        }
    }

    /**
     * 数値フォーマットチェック検証（統一版）
     * 0.01から999.99の範囲で、整数部分1-3桁、小数部分必須2桁の形式を強制
     */
    private static void validateDecimalFormat(String fieldName, String value, ValidationRule rule, List<ValidationError> errors) {
        // フォーマットチェック：正規表現による形式検証
        if (!DECIMAL_FORMAT_PATTERN.matcher(value).matches()) {
            addDecimalFormatError(fieldName, value, rule, errors);
            return;
        }

        try {
            // 数値範囲チェック：BigDecimalによる精密な数値比較
            java.math.BigDecimal decimal = new java.math.BigDecimal(value);
            if (decimal.compareTo(DECIMAL_MIN_VALUE) < 0 || decimal.compareTo(DECIMAL_MAX_VALUE) > 0) {
                addDecimalFormatError(fieldName, value, rule, errors);
            }
        } catch (NumberFormatException e) {
            // 数値変換エラーの場合もフォーマットエラーとして処理
            addDecimalFormatError(fieldName, value, rule, errors);
        }
    }

    /**
     * 数値フォーマットエラーを追加するヘルパーメソッド
     */
    private static void addDecimalFormatError(String fieldName, String value, ValidationRule rule, List<ValidationError> errors) {
        String displayName = rule.hasCustomFieldName() ? rule.getCustomFieldName() : fieldName;

        String message = ErrorMessageResolver.resolveMessage(
            rule.getErrorCode(),
            rule.getMessageTemplate(),
            ErrorMessageResolver.ValidationType.DECIMAL_FORMAT,
            displayName
        );

        errors.add(new ValidationError(fieldName, displayName, value, message, rule.getErrorCode()));
    }

    /**
     * 範囲チェック検証（統一版）
     * min と max が等しい場合は精確な長さチェックを実行し、異なる場合は従来の範囲チェックを実行
     */
    private static void validateRange(String fieldName, Object value, ValidationRule rule, List<ValidationError> errors) {
        long[] range = rule.getRangeParameter();
        long min = range[0];
        long max = range[1];

        // min と max が等しい場合は精確な長さチェックを実行
        if (min == max) {
            validateExactLength(fieldName, value, rule, min, errors);
        } else {
            // 従来の長さ範囲チェックを実行
            validateLengthRange(fieldName, value, rule, min, max, errors);
        }
    }

    /**
     * 精確な長さチェック検証
     * 入力値の文字列長が指定された値と完全に一致するかをチェック
     */
    private static void validateExactLength(String fieldName, Object value, ValidationRule rule, long expectedLength, List<ValidationError> errors) {
        String stringValue = value.toString();
        int actualLength = stringValue.length();

        if (actualLength != expectedLength) {
            String displayName = rule.hasCustomFieldName() ? rule.getCustomFieldName() : fieldName;

            String message = ErrorMessageResolver.resolveMessage(
                rule.getErrorCode(),
                rule.getMessageTemplate(),
                ErrorMessageResolver.ValidationType.RANGE,
                displayName,
                expectedLength,
                expectedLength
            );

            errors.add(new ValidationError(fieldName, displayName, value, message, rule.getErrorCode()));
        }
    }

    /**
     * 長さ範囲チェック検証
     * 従来の範囲チェックロジック（文字列長が指定された範囲内にあるかをチェック）
     */
    private static void validateLengthRange(String fieldName, Object value, ValidationRule rule, long min, long max, List<ValidationError> errors) {
        String stringValue = value.toString();
        int actualLength = stringValue.length();

        if (actualLength < min || actualLength > max) {
            String displayName = rule.hasCustomFieldName() ? rule.getCustomFieldName() : fieldName;

            String message = ErrorMessageResolver.resolveMessage(
                rule.getErrorCode(),
                rule.getMessageTemplate(),
                ErrorMessageResolver.ValidationType.RANGE,
                displayName,
                min,
                max
            );

            errors.add(new ValidationError(fieldName, displayName, value, message, rule.getErrorCode()));
        }
    }


}