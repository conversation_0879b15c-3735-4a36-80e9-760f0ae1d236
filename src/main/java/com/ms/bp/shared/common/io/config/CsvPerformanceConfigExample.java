package com.ms.bp.shared.common.io.config;

import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.io.model.FileFormat;
import com.ms.bp.shared.common.io.options.ExportOptions;

/**
 * CSV書き込みパフォーマンス設定例
 * CsvWriteStrategyは内部でBusinessConstantsの値を使用してパフォーマンス最適化を実行
 * このクラスは設定例として残しているが、実際の性能設定はCsvWriteStrategy内部で自動適用される
 */
public class CsvPerformanceConfigExample {

    /**
     * 標準設定（デフォルト）
     * CsvWriteStrategyが内部で自動的にパフォーマンス最適化を適用
     *
     * @return 標準設定のExportOptions
     */
    public static ExportOptions createStandardConfig() {
        return ExportOptions.builder()
                .format(FileFormat.CSV)
                .includeHeader(true)
                .batchSize(BusinessConstants.CSV_DOWNLOAD_BATCH_SIZE)
                .build();
    }
    
    /**
     * 高性能設定
     * CsvWriteStrategyが内部で自動的にパフォーマンス最適化を適用
     *
     * @return 高性能設定のExportOptions（実際の最適化は内部で自動実行）
     */
    public static ExportOptions createHighPerformanceConfig() {
        return ExportOptions.builder()
                .format(FileFormat.CSV)
                .includeHeader(true)
                .batchSize(BusinessConstants.CSV_DOWNLOAD_BATCH_SIZE)
                .build();
    }

    /**
     * 省メモリ設定
     * CsvWriteStrategyが内部で自動的にパフォーマンス最適化を適用
     *
     * @return 省メモリ設定のExportOptions（実際の最適化は内部で自動実行）
     */
    public static ExportOptions createLowMemoryConfig() {
        return ExportOptions.builder()
                .format(FileFormat.CSV)
                .includeHeader(true)
                .batchSize(BusinessConstants.CSV_DOWNLOAD_BATCH_SIZE)
                .build();
    }

    /**
     * 互換性重視設定
     * CsvWriteStrategyが内部で自動的にパフォーマンス最適化を適用
     *
     * @return 互換性重視設定のExportOptions（実際の最適化は内部で自動実行）
     */
    public static ExportOptions createCompatibilityConfig() {
        return ExportOptions.builder()
                .format(FileFormat.CSV)
                .includeHeader(true)
                .batchSize(BusinessConstants.CSV_DOWNLOAD_BATCH_SIZE)
                .build();
    }
    
    /**
     * カスタム設定ビルダー
     * CsvWriteStrategyが内部で自動的にパフォーマンス最適化を適用
     *
     * @param recordCount 予想レコード数（参考情報として保持）
     * @param memoryConstraint メモリ制約レベル（参考情報として保持）
     * @return カスタマイズされたExportOptions（実際の最適化は内部で自動実行）
     */
    public static ExportOptions createCustomConfig(int recordCount, int memoryConstraint) {
        // 実際のパフォーマンス設定はCsvWriteStrategy内部で自動適用される
        // ここでは基本的な設定のみ行う
        return ExportOptions.builder()
                .format(FileFormat.CSV)
                .includeHeader(true)
                .batchSize(BusinessConstants.CSV_DOWNLOAD_BATCH_SIZE)
                .build();
    }
    
    /**
     * 設定情報をログ出力用文字列として取得
     * パフォーマンス設定はCsvWriteStrategy内部で自動適用される
     *
     * @param options ExportOptions
     * @return 設定情報文字列
     */
    public static String getConfigSummary(ExportOptions options) {
        return String.format(
            "CSV書き込み設定: format=%s, includeHeader=%s, batchSize=%d (パフォーマンス最適化は内部で自動適用)",
            options.getFormat(),
            options.isIncludeHeader(),
            options.getBatchSize()
        );
    }
}
