package com.ms.bp.shared.common.util;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.shared.common.exception.TimeoutApproachingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Lambda実行時間監視ユーティリティクラス
 * AWS Lambda関数の実行時間を監視し、タイムアウト接近時に適切な処理を実行する
 */
public class TimeoutMonitor {
    
    private static final Logger logger = LoggerFactory.getLogger(TimeoutMonitor.class);
    
    /**
     * デフォルトのタイムアウト緩衝時間（ミリ秒）
     * 状態更新やクリーンアップ処理に必要な時間を確保
     */
    private static final long DEFAULT_BUFFER_TIME_MS = 60000; // 60秒
    
    /**
     * Lambda実行コンテキスト
     */
    private final Context lambdaContext;
    
    /**
     * タイムアウト緩衝時間（ミリ秒）
     */
    private final long bufferTimeMs;
    
    /**
     * 監視開始時刻（ミリ秒）
     */
    private final long startTimeMs;
    
    /**
     * ジョブID（ログ出力用）
     */
    private final String jobId;
    
    /**
     * デフォルト緩衝時間を使用するコンストラクタ
     * 
     * @param lambdaContext Lambda実行コンテキスト
     * @param jobId ジョブID（ログ出力用）
     */
    public TimeoutMonitor(Context lambdaContext, String jobId) {
        this(lambdaContext, jobId, DEFAULT_BUFFER_TIME_MS);
    }
    
    /**
     * カスタム緩衝時間を指定するコンストラクタ
     * 
     * @param lambdaContext Lambda実行コンテキスト
     * @param jobId ジョブID（ログ出力用）
     * @param bufferTimeMs タイムアウト緩衝時間（ミリ秒）
     */
    public TimeoutMonitor(Context lambdaContext, String jobId, long bufferTimeMs) {
        if (lambdaContext == null) {
            throw new IllegalArgumentException("Lambda contextは必須です");
        }
        if (jobId == null || jobId.trim().isEmpty()) {
            throw new IllegalArgumentException("ジョブIDは必須です");
        }
        if (bufferTimeMs < 0) {
            throw new IllegalArgumentException("緩衝時間は0以上である必要があります");
        }
        
        this.lambdaContext = lambdaContext;
        this.jobId = jobId;
        this.bufferTimeMs = bufferTimeMs;
        this.startTimeMs = System.currentTimeMillis();
        
        logger.debug("TimeoutMonitor初期化: jobId={}, bufferTime={}ms", jobId, bufferTimeMs);
    }
    
    /**
     * タイムアウト接近チェックを実行
     * タイムアウトに接近している場合、TimeoutApproachingExceptionを投げる
     * 
     * @param checkPointName チェックポイント名（ログ出力用）
     * @throws TimeoutApproachingException タイムアウトに接近している場合
     */
    public void checkTimeout(String checkPointName) throws TimeoutApproachingException {
        if (isTimeoutApproaching()) {
            long remainingTimeMs = getRemainingTimeMs();
            long elapsedTimeMs = getElapsedTimeMs();
            
            logger.warn("Lambda タイムアウト接近を検出: jobId={}, checkPoint={}, " +
                       "remainingTime={}ms, elapsedTime={}ms, bufferTime={}ms",
                       jobId, checkPointName, remainingTimeMs, elapsedTimeMs, bufferTimeMs);
            
            throw new TimeoutApproachingException(jobId, checkPointName, remainingTimeMs, elapsedTimeMs);
        }
        
        // デバッグログ：正常な場合の時間情報
        if (logger.isDebugEnabled()) {
            logger.debug("タイムアウトチェック通過: jobId={}, checkPoint={}, remainingTime={}ms",
                        jobId, checkPointName, getRemainingTimeMs());
        }
    }
    
    /**
     * タイムアウトに接近しているかを判定
     * 
     * @return タイムアウトに接近している場合はtrue
     */
    public boolean isTimeoutApproaching() {
        return getRemainingTimeMs() <= bufferTimeMs;
    }
    
    /**
     * Lambda関数の残り実行時間を取得（ミリ秒）
     * 
     * @return 残り実行時間（ミリ秒）
     */
    public long getRemainingTimeMs() {
        return lambdaContext.getRemainingTimeInMillis();
    }
    
    /**
     * 監視開始からの経過時間を取得（ミリ秒）
     * 
     * @return 経過時間（ミリ秒）
     */
    public long getElapsedTimeMs() {
        return System.currentTimeMillis() - startTimeMs;
    }
    
    /**
     * 設定されている緩衝時間を取得（ミリ秒）
     * 
     * @return 緩衝時間（ミリ秒）
     */
    public long getBufferTimeMs() {
        return bufferTimeMs;
    }
    
    /**
     * ジョブIDを取得
     * 
     * @return ジョブID
     */
    public String getJobId() {
        return jobId;
    }
    
    /**
     * 実行時間統計情報をログ出力
     * 処理完了時やエラー時の診断情報として使用
     */
    public void logExecutionStats() {
        long remainingTimeMs = getRemainingTimeMs();
        long elapsedTimeMs = getElapsedTimeMs();
        double remainingTimePercent = (double) remainingTimeMs / (remainingTimeMs + elapsedTimeMs) * 100;
        
        logger.info("Lambda実行時間統計: jobId={}, elapsedTime={}ms, remainingTime={}ms, " +
                   "remainingPercent={:.1f}%, bufferTime={}ms",
                   jobId, elapsedTimeMs, remainingTimeMs, remainingTimePercent, bufferTimeMs);
    }
    
    /**
     * 安全な実行時間チェック
     * 指定された処理時間が残り時間内で実行可能かを判定
     * 
     * @param estimatedProcessingTimeMs 推定処理時間（ミリ秒）
     * @return 安全に実行可能な場合はtrue
     */
    public boolean canSafelyExecute(long estimatedProcessingTimeMs) {
        return getRemainingTimeMs() > (estimatedProcessingTimeMs + bufferTimeMs);
    }
    
    /**
     * 実行時間の警告レベルを取得
     * 
     * @return 警告レベル（INFO, WARN, ERROR）
     */
    public String getWarningLevel() {
        long remainingTimeMs = getRemainingTimeMs();
        
        if (remainingTimeMs <= bufferTimeMs) {
            return "ERROR";
        } else if (remainingTimeMs <= bufferTimeMs * 2) {
            return "WARN";
        } else {
            return "INFO";
        }
    }
}
