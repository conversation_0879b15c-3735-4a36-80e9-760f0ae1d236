package com.ms.bp.shared.common.util;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.shared.common.constants.BusinessConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * BackgroundTimeoutMonitor使用例
 * Lambda関数での後台スレッド監視の実装パターンを示すサンプルクラス
 * 
 * 注意：このクラスは実装例として提供されており、実際のプロダクションコードでは
 * TaskOrchestrationServiceで既に統合されています。
 */
public class BackgroundTimeoutMonitorExample {
    
    private static final Logger logger = LoggerFactory.getLogger(BackgroundTimeoutMonitorExample.class);
    
    /**
     * 基本的なBackgroundTimeoutMonitor使用例（エクスポート処理）
     * 
     * @param context Lambda実行コンテキスト
     * @param jobId ジョブID
     */
    public void basicExportUsageExample(Context context, String jobId) {
        // 後台タイムアウト監視を初期化
        BackgroundTimeoutMonitor monitor = new BackgroundTimeoutMonitor(
            context, jobId, BusinessConstants.OPERATION_DOWNLOAD_CODE);
        
        try {
            // 後台監視開始
            monitor.startMonitoring();
            logger.info("エクスポート処理開始: jobId={}", jobId);
            
            // 処理1: データ取得
            monitor.updateCurrentPhase("データ取得");
            performDataRetrieval();
            
            // タイムアウト検出チェック
            if (monitor.isTimeoutDetected()) {
                logger.warn("データ取得中にタイムアウト検出: jobId={}", jobId);
                return; // 処理を中断
            }
            
            // 処理2: データ変換
            monitor.updateCurrentPhase("データ変換");
            performDataTransformation();
            
            if (monitor.isTimeoutDetected()) {
                logger.warn("データ変換中にタイムアウト検出: jobId={}", jobId);
                return;
            }
            
            // 処理3: ファイル生成
            monitor.updateCurrentPhase("ファイル生成");
            performFileGeneration();
            
            if (monitor.isTimeoutDetected()) {
                logger.warn("ファイル生成中にタイムアウト検出: jobId={}", jobId);
                return;
            }
            
            // 処理4: S3アップロード
            monitor.updateCurrentPhase("S3アップロード");
            performS3Upload();
            
            logger.info("エクスポート処理完了: jobId={}", jobId);
            
        } finally {
            // 重要：必ずクリーンアップを実行
            monitor.cleanup();
            logger.debug("後台監視クリーンアップ完了: jobId={}", jobId);
        }
    }
    
    /**
     * 基本的なBackgroundTimeoutMonitor使用例（インポート処理）
     * 
     * @param context Lambda実行コンテキスト
     * @param jobId ジョブID
     */
    public void basicImportUsageExample(Context context, String jobId) {
        // 後台タイムアウト監視を初期化（インポート操作）
        BackgroundTimeoutMonitor monitor = new BackgroundTimeoutMonitor(
            context, jobId, BusinessConstants.OPERATION_UPLOAD_CODE);
        
        try {
            // 後台監視開始
            monitor.startMonitoring();
            logger.info("インポート処理開始: jobId={}", jobId);
            
            // 処理1: ファイル検証
            monitor.updateCurrentPhase("ファイル検証");
            performFileValidation();
            
            if (monitor.isTimeoutDetected()) {
                logger.warn("ファイル検証中にタイムアウト検出: jobId={}", jobId);
                return;
            }
            
            // 処理2: データ解析
            monitor.updateCurrentPhase("データ解析");
            performDataParsing();
            
            if (monitor.isTimeoutDetected()) {
                logger.warn("データ解析中にタイムアウト検出: jobId={}", jobId);
                return;
            }
            
            // 処理3: データベース更新
            monitor.updateCurrentPhase("データベース更新");
            performDatabaseUpdate();
            
            logger.info("インポート処理完了: jobId={}", jobId);
            
        } finally {
            // 重要：必ずクリーンアップを実行
            monitor.cleanup();
            logger.debug("後台監視クリーンアップ完了: jobId={}", jobId);
        }
    }
    
    /**
     * カスタム緩衝時間を使用する例
     * 
     * @param context Lambda実行コンテキスト
     * @param jobId ジョブID
     */
    public void customBufferTimeExample(Context context, String jobId) {
        // カスタム緩衝時間（90秒）を指定
        long customBufferTimeMs = 90000L; // 90秒
        BackgroundTimeoutMonitor monitor = new BackgroundTimeoutMonitor(
            context, jobId, BusinessConstants.OPERATION_DOWNLOAD_CODE, customBufferTimeMs);
        
        try {
            monitor.startMonitoring();
            logger.info("カスタム緩衝時間での処理開始: jobId={}, bufferTime={}ms", 
                       jobId, customBufferTimeMs);
            
            // 長時間処理の場合は緩衝時間を長めに設定
            monitor.updateCurrentPhase("長時間処理");
            performLongRunningProcess();
            
            if (monitor.isTimeoutDetected()) {
                logger.warn("長時間処理中にタイムアウト検出: jobId={}", jobId);
                return;
            }
            
            logger.info("カスタム緩衝時間での処理完了: jobId={}", jobId);
            
        } finally {
            monitor.cleanup();
        }
    }
    
    /**
     * 例外処理を含む包括的な使用例
     * 
     * @param context Lambda実行コンテキスト
     * @param jobId ジョブID
     */
    public void comprehensiveUsageExample(Context context, String jobId) {
        BackgroundTimeoutMonitor monitor = new BackgroundTimeoutMonitor(
            context, jobId, BusinessConstants.OPERATION_DOWNLOAD_CODE);
        
        try {
            monitor.startMonitoring();
            logger.info("包括的処理開始: jobId={}", jobId);
            
            // フェーズ1: 初期化
            monitor.updateCurrentPhase("初期化フェーズ");
            performInitialization();
            
            if (monitor.isTimeoutDetected()) {
                logger.warn("初期化フェーズでタイムアウト検出: jobId={}", jobId);
                handleTimeoutCleanup();
                return;
            }
            
            // フェーズ2: メイン処理
            monitor.updateCurrentPhase("メイン処理フェーズ");
            performMainProcessing();
            
            if (monitor.isTimeoutDetected()) {
                logger.warn("メイン処理フェーズでタイムアウト検出: jobId={}", jobId);
                handleTimeoutCleanup();
                return;
            }
            
            // フェーズ3: 最終化
            monitor.updateCurrentPhase("最終化フェーズ");
            performFinalization();
            
            if (monitor.isTimeoutDetected()) {
                logger.warn("最終化フェーズでタイムアウト検出: jobId={}", jobId);
                handleTimeoutCleanup();
                return;
            }
            
            logger.info("包括的処理完了: jobId={}", jobId);
            
        } catch (Exception e) {
            logger.error("処理中にエラーが発生: jobId={}", jobId, e);
            
            // エラー発生時でもタイムアウト状態をチェック
            if (monitor.isTimeoutDetected()) {
                logger.warn("エラー発生時にタイムアウトも検出: jobId={}", jobId);
            }
            
            // 適切なエラー処理
            handleErrorCleanup();
            
        } finally {
            // 統計情報をログ出力
            monitor.logMonitoringStats();
            
            // 必ずクリーンアップを実行
            monitor.cleanup();
            logger.debug("包括的処理のクリーンアップ完了: jobId={}", jobId);
        }
    }
    
    /**
     * 複数の監視インスタンスを使用する例（非推奨）
     * 注意：通常は1つのジョブに対して1つの監視インスタンスを使用してください
     * 
     * @param context Lambda実行コンテキスト
     * @param jobId ジョブID
     */
    public void multipleMonitorExample(Context context, String jobId) {
        logger.warn("複数監視インスタンスの使用は推奨されません: jobId={}", jobId);
        
        // 注意：これは例示目的のみで、実際の使用は推奨されません
        BackgroundTimeoutMonitor monitor1 = new BackgroundTimeoutMonitor(
            context, jobId + "_part1", BusinessConstants.OPERATION_DOWNLOAD_CODE);
        BackgroundTimeoutMonitor monitor2 = new BackgroundTimeoutMonitor(
            context, jobId + "_part2", BusinessConstants.OPERATION_DOWNLOAD_CODE);
        
        try {
            monitor1.startMonitoring();
            monitor2.startMonitoring();
            
            // 並行処理（推奨されない）
            performParallelProcessing();
            
        } finally {
            // 両方のクリーンアップが必要
            monitor1.cleanup();
            monitor2.cleanup();
        }
    }
    
    // ==================== プライベートメソッド（サンプル処理） ====================
    
    private void performDataRetrieval() {
        logger.debug("データ取得処理を実行中...");
        simulateProcessing(2000);
    }
    
    private void performDataTransformation() {
        logger.debug("データ変換処理を実行中...");
        simulateProcessing(3000);
    }
    
    private void performFileGeneration() {
        logger.debug("ファイル生成処理を実行中...");
        simulateProcessing(4000);
    }
    
    private void performS3Upload() {
        logger.debug("S3アップロード処理を実行中...");
        simulateProcessing(3000);
    }
    
    private void performFileValidation() {
        logger.debug("ファイル検証処理を実行中...");
        simulateProcessing(1500);
    }
    
    private void performDataParsing() {
        logger.debug("データ解析処理を実行中...");
        simulateProcessing(2500);
    }
    
    private void performDatabaseUpdate() {
        logger.debug("データベース更新処理を実行中...");
        simulateProcessing(2000);
    }
    
    private void performLongRunningProcess() {
        logger.debug("長時間処理を実行中...");
        simulateProcessing(8000);
    }
    
    private void performInitialization() {
        logger.debug("初期化処理を実行中...");
        simulateProcessing(1000);
    }
    
    private void performMainProcessing() {
        logger.debug("メイン処理を実行中...");
        simulateProcessing(5000);
    }
    
    private void performFinalization() {
        logger.debug("最終化処理を実行中...");
        simulateProcessing(1500);
    }
    
    private void performParallelProcessing() {
        logger.debug("並行処理を実行中...");
        simulateProcessing(3000);
    }
    
    private void handleTimeoutCleanup() {
        logger.info("タイムアウト時のクリーンアップ処理を実行中...");
        simulateProcessing(500);
    }
    
    private void handleErrorCleanup() {
        logger.info("エラー時のクリーンアップ処理を実行中...");
        simulateProcessing(500);
    }
    
    /**
     * 処理時間をシミュレートするヘルパーメソッド
     * 
     * @param durationMs 処理時間（ミリ秒）
     */
    private void simulateProcessing(long durationMs) {
        try {
            Thread.sleep(durationMs);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("処理がインタラプトされました", e);
        }
    }
}
