# Lambda実行時間後台監視機能実装ガイド

## 概要

AWS Lambda関数の実行時間がタイムアウトに接近した際に、適切なクリーンアップ処理と状態更新を実行するための後台スレッド監視機能を実装しました。この機能は導入（Import）と導出（Export）操作専用に設計されており、自動的なタイムアウト検出と履歴表状態更新を提供します。

## 実装コンポーネント

### 1. BackgroundTimeoutMonitor（後台タイムアウト監視クラス）

**場所**: `src/main/java/com/ms/bp/shared/common/util/BackgroundTimeoutMonitor.java`

**主要機能**:
- 独立したデーモンスレッドでの自動監視
- 30秒間隔での定期的なタイムアウトチェック
- 操作タイプ別の履歴表状態自動更新
- スレッドライフサイクルの完全管理
- 処理フェーズの追跡とログ出力

**使用例**:
```java
// エクスポート処理での使用
BackgroundTimeoutMonitor monitor = new BackgroundTimeoutMonitor(
    context, jobId, BusinessConstants.OPERATION_DOWNLOAD_CODE);

try {
    monitor.startMonitoring();
    // ビジネス処理
    monitor.updateCurrentPhase("データ処理");
    performBusinessLogic();
} finally {
    monitor.cleanup(); // 重要：必ずクリーンアップ
}
```

### 2. 後台監視の技術特徴

**スレッド管理**:
- デーモンスレッドとして実行（Lambda終了時に自動終了）
- スレッド間通信にvolatile変数とAtomicBooleanを使用
- 適切なスレッド終了待機とタイムアウト処理

**監視ロジック**:
- 30秒間隔での定期チェック
- 緩衝時間（デフォルト60秒）による早期警告
- 処理フェーズ別の詳細ログ出力

**リソース管理**:
- 確実なスレッドクリーンアップ
- メモリリーク防止機構
- 例外安全なリソース解放

### 3. TaskOrchestrationService（統合実装）

**場所**: `src/main/java/com/ms/bp/application/data/TaskOrchestrationService.java`

**修正内容**:
- エクスポート/インポート処理での BackgroundTimeoutMonitor 統合
- 各処理フェーズでのタイムアウト検出チェック
- 後台監視による自動的な履歴表状態更新
- 確実なスレッドクリーンアップ機構

## 実装の特徴

### DDD アーキテクチャ準拠

- **Application層**: TaskOrchestrationService での後台監視統合
- **Shared層**: BackgroundTimeoutMonitor の共通監視機能
- **Infrastructure層**: 既存の JobStatusService との自動連携

### 自動化された監視

- 手動チェックポイント不要
- 全処理段階の自動カバレッジ
- 開発者の監視忘れリスク排除

### 堅牢なスレッド管理

- 確実なリソースクリーンアップ
- 例外安全なスレッド終了
- メモリリーク防止機構

## 使用方法

### 1. 基本的な統合パターン（自動統合済み）

**注意**: TaskOrchestrationService では既に後台監視が統合されているため、
開発者が手動で監視を追加する必要はありません。

```java
// TaskOrchestrationService での自動統合例
public void orchestrateExportTask(Long rrkBango, ExportRequest exportRequest,
                                UserInfo userInfo, Context context) {
    // 後台監視が自動的に開始される
    BackgroundTimeoutMonitor backgroundMonitor = new BackgroundTimeoutMonitor(
        context, rrkBango.toString(), BusinessConstants.OPERATION_DOWNLOAD_CODE);

    try {
        backgroundMonitor.startMonitoring();
        // ビジネス処理（自動監視下で実行）
        executeExportFlow(rrkBango, exportRequest, userInfo, context, backgroundMonitor);
    } finally {
        backgroundMonitor.cleanup(); // 自動クリーンアップ
    }
}
```

### 2. 処理フェーズの更新（推奨）

```java
// ビジネス処理内でのフェーズ更新
private void executeExportFlow(Long rrkBango, ExportRequest exportRequest,
                             UserInfo userInfo, Context context,
                             BackgroundTimeoutMonitor backgroundMonitor) {
    // フェーズ1: データ処理
    backgroundMonitor.updateCurrentPhase("データ処理");
    performDataProcessing();

    // タイムアウト検出チェック
    if (backgroundMonitor.isTimeoutDetected()) {
        logger.warn("データ処理中にタイムアウト検出");
        return; // 処理を安全に中断
    }

    // フェーズ2: ファイル生成
    backgroundMonitor.updateCurrentPhase("ファイル生成");
    performFileGeneration();

    if (backgroundMonitor.isTimeoutDetected()) {
        logger.warn("ファイル生成中にタイムアウト検出");
        return;
    }
}
```

### 3. カスタム監視設定

```java
// カスタム緩衝時間での監視
BackgroundTimeoutMonitor monitor = new BackgroundTimeoutMonitor(
    context, jobId, operationType, 90000L); // 90秒緩衝時間
```

## 設定パラメータ

### 緩衝時間設定

| 処理タイプ | 推奨緩衝時間 | 理由 |
|-----------|-------------|------|
| 軽量処理 | 30-45秒 | 状態更新とクリーンアップに十分 |
| 標準処理 | 60秒（デフォルト） | バランスの取れた設定 |
| 重量処理 | 90-120秒 | 複雑なクリーンアップ処理に対応 |

### チェックポイント設定

- **処理開始前**: 必須チェックポイント
- **データ処理前**: 大量データ処理前
- **S3操作前**: ファイルアップロード/ダウンロード前
- **状態更新前**: データベース更新前

## エラーハンドリング

### 1. TimeoutApproachingException の処理

```java
try {
    // ビジネス処理
} catch (TimeoutApproachingException e) {
    // ログ出力
    logger.warn("タイムアウト接近: {}", e.getLogInfo());
    
    // 履歴表更新
    updateJobStatus(jobId, BATCH_STATUS_SYSTEM_ERROR_CODE);
    
    // 必要に応じて再スロー
    throw e;
}
```

### 2. GlobalExceptionHandler での統合

TimeoutApproachingException は GlobalExceptionHandler で自動的に処理され、適切なエラーレスポンスが生成されます。

## テスト戦略

### 1. 単体テスト

- **TimeoutMonitorTest**: 基本機能のテスト
- **TimeoutApproachingExceptionTest**: 例外クラスのテスト

### 2. 統合テスト

- **TaskOrchestrationServiceTimeoutTest**: 実際の処理フローでのテスト

### 3. テスト実行

```bash
# 単体テスト実行
mvn test -Dtest=TimeoutMonitorTest
mvn test -Dtest=TimeoutApproachingExceptionTest

# 統合テスト実行
mvn test -Dtest=TaskOrchestrationServiceTimeoutTest
```

## 監視とログ

### 1. ログレベル

- **DEBUG**: タイムアウトチェック通過
- **INFO**: 実行時間統計
- **WARN**: タイムアウト接近警告
- **ERROR**: 緊急レベルのタイムアウト

### 2. ログ出力例

```
INFO  - TimeoutMonitor初期化完了: rrkBango=12345, bufferTime=60000ms
DEBUG - タイムアウトチェック通過: jobId=12345, checkPoint=データ処理開始前, remainingTime=180000ms
WARN  - Lambda タイムアウト接近を検出: jobId=12345, checkPoint=S3アップロード開始前, remainingTime=45000ms
INFO  - Lambda実行時間統計: jobId=12345, elapsedTime=255000ms, remainingTime=45000ms, remainingPercent=15.0%
```

## パフォーマンス考慮事項

### 1. オーバーヘッド

- TimeoutMonitor の初期化: < 1ms
- checkTimeout() の実行: < 0.1ms
- 実行時間統計の計算: < 0.1ms

### 2. メモリ使用量

- TimeoutMonitor インスタンス: 約 200 bytes
- TimeoutApproachingException: 約 500 bytes

## トラブルシューティング

### 1. よくある問題

**問題**: タイムアウトチェックが頻繁に失敗する
**解決**: 緩衝時間を長めに設定するか、処理の最適化を検討

**問題**: 例外が適切にキャッチされない
**解決**: try-catch ブロックの配置を確認

### 2. デバッグ方法

```java
// デバッグログの有効化
logger.debug("残り時間: {}ms, 緩衝時間: {}ms, 接近判定: {}", 
            monitor.getRemainingTimeMs(), 
            monitor.getBufferTimeMs(),
            monitor.isTimeoutApproaching());
```

## 今後の拡張予定

1. **CloudWatch メトリクス統合**: タイムアウト発生率の監視
2. **自動スケーリング**: 処理量に応じた Lambda 設定調整
3. **分散処理対応**: 複数 Lambda 間でのタスク分割

## 関連ドキュメント

- [AWS Lambda 実行時間制限](https://docs.aws.amazon.com/lambda/latest/dg/configuration-timeout.html)
- [DDD アーキテクチャガイド](./ddd-architecture-guide.md)
- [エラーハンドリング標準](./error-handling-standards.md)
