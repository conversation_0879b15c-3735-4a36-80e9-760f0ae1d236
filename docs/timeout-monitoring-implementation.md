# Lambda実行時間タイムアウト監視機能実装ガイド

## 概要

AWS Lambda関数の実行時間がタイムアウトに接近した際に、適切なクリーンアップ処理と状態更新を実行するための予防的監視機能を実装しました。

## 実装コンポーネント

### 1. TimeoutMonitor（タイムアウト監視ユーティリティ）

**場所**: `src/main/java/com/ms/bp/shared/common/util/TimeoutMonitor.java`

**主要機能**:
- Lambda Context から残り実行時間を監視
- 設定可能な緩衝時間（デフォルト60秒）
- タイムアウト接近時の例外発生
- 実行時間統計の提供

**使用例**:
```java
// 基本的な使用方法
TimeoutMonitor monitor = new TimeoutMonitor(context, jobId);
monitor.checkTimeout("処理開始前");

// カスタム緩衝時間（90秒）
TimeoutMonitor monitor = new TimeoutMonitor(context, jobId, 90000L);
```

### 2. TimeoutApproachingException（タイムアウト接近例外）

**場所**: `src/main/java/com/ms/bp/shared/common/exception/TimeoutApproachingException.java`

**主要機能**:
- タイムアウト接近時の詳細情報保持
- 実行時間統計の提供
- ServiceException への変換機能
- ログ出力用の情報提供

**例外情報**:
- ジョブID
- チェックポイント名
- 残り実行時間
- 経過時間
- 実行時間使用率

### 3. TaskOrchestrationService（統合実装）

**場所**: `src/main/java/com/ms/bp/application/data/TaskOrchestrationService.java`

**修正内容**:
- エクスポート/インポート処理での TimeoutMonitor 統合
- 各処理段階でのタイムアウトチェック
- TimeoutApproachingException の適切な処理
- 履歴表状態の自動更新

## 実装の特徴

### DDD アーキテクチャ準拠

- **Application層**: TaskOrchestrationService でのビジネスフロー制御
- **Shared層**: TimeoutMonitor, TimeoutApproachingException の共通機能
- **Infrastructure層**: 既存の ExportJobStatusService との統合

### 非侵入的設計

- 既存のビジネスロジックを変更せず
- 既存の例外処理機構と統合
- WorkerHandler, DataApplicationService との互換性維持

### 設定可能な監視パラメータ

- 緩衝時間のカスタマイズ
- チェックポイントの柔軟な設定
- 警告レベルの段階的判定

## 使用方法

### 1. 基本的な統合パターン

```java
public void processTask(Context context, String jobId) {
    TimeoutMonitor monitor = new TimeoutMonitor(context, jobId);
    
    try {
        monitor.checkTimeout("処理1開始前");
        performStep1();
        
        monitor.checkTimeout("処理2開始前");
        performStep2();
        
        monitor.logExecutionStats();
        
    } catch (TimeoutApproachingException e) {
        logger.warn("タイムアウト接近: {}", e.getLogInfo());
        updateJobStatus(jobId, SYSTEM_ERROR);
        throw e;
    }
}
```

### 2. 安全実行チェック

```java
TimeoutMonitor monitor = new TimeoutMonitor(context, jobId);

if (monitor.canSafelyExecute(estimatedTimeMs)) {
    performLongRunningProcess();
} else {
    logger.warn("安全実行不可: 推定時間={}ms, 残り時間={}ms", 
               estimatedTimeMs, monitor.getRemainingTimeMs());
    performAlternativeProcess();
}
```

### 3. 警告レベル監視

```java
String level = monitor.getWarningLevel();
switch (level) {
    case "INFO": // 正常範囲
        break;
    case "WARN": // 注意レベル
        adjustProcessingPriority();
        break;
    case "ERROR": // 危険レベル
        switchToEmergencyMode();
        break;
}
```

## 設定パラメータ

### 緩衝時間設定

| 処理タイプ | 推奨緩衝時間 | 理由 |
|-----------|-------------|------|
| 軽量処理 | 30-45秒 | 状態更新とクリーンアップに十分 |
| 標準処理 | 60秒（デフォルト） | バランスの取れた設定 |
| 重量処理 | 90-120秒 | 複雑なクリーンアップ処理に対応 |

### チェックポイント設定

- **処理開始前**: 必須チェックポイント
- **データ処理前**: 大量データ処理前
- **S3操作前**: ファイルアップロード/ダウンロード前
- **状態更新前**: データベース更新前

## エラーハンドリング

### 1. TimeoutApproachingException の処理

```java
try {
    // ビジネス処理
} catch (TimeoutApproachingException e) {
    // ログ出力
    logger.warn("タイムアウト接近: {}", e.getLogInfo());
    
    // 履歴表更新
    updateJobStatus(jobId, BATCH_STATUS_SYSTEM_ERROR_CODE);
    
    // 必要に応じて再スロー
    throw e;
}
```

### 2. GlobalExceptionHandler での統合

TimeoutApproachingException は GlobalExceptionHandler で自動的に処理され、適切なエラーレスポンスが生成されます。

## テスト戦略

### 1. 単体テスト

- **TimeoutMonitorTest**: 基本機能のテスト
- **TimeoutApproachingExceptionTest**: 例外クラスのテスト

### 2. 統合テスト

- **TaskOrchestrationServiceTimeoutTest**: 実際の処理フローでのテスト

### 3. テスト実行

```bash
# 単体テスト実行
mvn test -Dtest=TimeoutMonitorTest
mvn test -Dtest=TimeoutApproachingExceptionTest

# 統合テスト実行
mvn test -Dtest=TaskOrchestrationServiceTimeoutTest
```

## 監視とログ

### 1. ログレベル

- **DEBUG**: タイムアウトチェック通過
- **INFO**: 実行時間統計
- **WARN**: タイムアウト接近警告
- **ERROR**: 緊急レベルのタイムアウト

### 2. ログ出力例

```
INFO  - TimeoutMonitor初期化完了: rrkBango=12345, bufferTime=60000ms
DEBUG - タイムアウトチェック通過: jobId=12345, checkPoint=データ処理開始前, remainingTime=180000ms
WARN  - Lambda タイムアウト接近を検出: jobId=12345, checkPoint=S3アップロード開始前, remainingTime=45000ms
INFO  - Lambda実行時間統計: jobId=12345, elapsedTime=255000ms, remainingTime=45000ms, remainingPercent=15.0%
```

## パフォーマンス考慮事項

### 1. オーバーヘッド

- TimeoutMonitor の初期化: < 1ms
- checkTimeout() の実行: < 0.1ms
- 実行時間統計の計算: < 0.1ms

### 2. メモリ使用量

- TimeoutMonitor インスタンス: 約 200 bytes
- TimeoutApproachingException: 約 500 bytes

## トラブルシューティング

### 1. よくある問題

**問題**: タイムアウトチェックが頻繁に失敗する
**解決**: 緩衝時間を長めに設定するか、処理の最適化を検討

**問題**: 例外が適切にキャッチされない
**解決**: try-catch ブロックの配置を確認

### 2. デバッグ方法

```java
// デバッグログの有効化
logger.debug("残り時間: {}ms, 緩衝時間: {}ms, 接近判定: {}", 
            monitor.getRemainingTimeMs(), 
            monitor.getBufferTimeMs(),
            monitor.isTimeoutApproaching());
```

## 今後の拡張予定

1. **CloudWatch メトリクス統合**: タイムアウト発生率の監視
2. **自動スケーリング**: 処理量に応じた Lambda 設定調整
3. **分散処理対応**: 複数 Lambda 間でのタスク分割

## 関連ドキュメント

- [AWS Lambda 実行時間制限](https://docs.aws.amazon.com/lambda/latest/dg/configuration-timeout.html)
- [DDD アーキテクチャガイド](./ddd-architecture-guide.md)
- [エラーハンドリング標準](./error-handling-standards.md)
